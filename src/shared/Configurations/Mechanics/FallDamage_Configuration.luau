--!strict

--[[
    - file: FallDamage_Configuration.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

   	- description:
      - Physics-accurate fall damage configuration system
      - Based on real-world biomechanics and material science
      - Implements evidence-based injury thresholds and material properties
      - Designed for maximum realism while maintaining gameplay balance

    - physics_basis:
      - Human injury thresholds based on medical research
      - Material properties from engineering databases
      - Terminal velocity calculations for human body dynamics
      - Impact force biomechanics from crash test data

    - references:
      - Abbreviated Injury Scale (AIS) for trauma classification
      - Material property databases from engineering handbooks
      - Biomechanical research on human impact tolerance
      - NASA human factors design standards
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ReplicatedStorage.Mechanics.FallDamage.FallDamage_Types)

-- ============================================================================
-- REAL-WORLD PHYSICS CONSTANTS
-- ============================================================================
local PHYSICS_CONSTANTS: Types.PhysicsConstants = {
	-- ========================================================================
	-- GRAVITATIONAL AND ATMOSPHERIC CONSTANTS
	-- ========================================================================

	-- Standard Earth gravity (Roblox uses 196.2 studs/s² ≈ 9.81 m/s²)
	GRAVITY = 196.2, 					-- studs/s²

	-- Air density at sea level
	AIR_DENSITY = 1.225, 				-- kg/m³ at 15°C, 1 atm

	-- Atmospheric pressure at sea level
	ATMOSPHERIC_PRESSURE = 101325, 		-- Pa (N/m²)

	-- ========================================================================
	-- HUMAN BIOMECHANICAL CONSTANTS
	-- ========================================================================

	-- Average human body properties
	HUMAN_MASS_AVERAGE = 70, 			-- kg (global average adult)
	HUMAN_MASS_RANGE = {
		CHILD = 30,      				-- kg (6-12 years)
		TEEN = 55,       				-- kg (13-17 years)
		ADULT_LIGHT = 60, 				-- kg (light adult)
		ADULT_AVERAGE = 70, 			-- kg (average adult)
		ADULT_HEAVY = 90,  				-- kg (heavy adult)
		ATHLETE = 85,    				-- kg (athletic build)
	},

	-- Human body aerodynamic properties
	HUMAN_DRAG_COEFFICIENT = 0.6, 		-- Upright falling position
	HUMAN_DRAG_COEFFICIENT_SPREAD = 1.0,-- Spread-eagle position
	HUMAN_CROSS_SECTIONAL_AREA = 0.7, 	-- m² (frontal area, upright)
	HUMAN_CROSS_SECTIONAL_AREA_SPREAD = 1.2, -- m² (spread-eagle)

	-- Human terminal velocities (real-world data)
	HUMAN_TERMINAL_VELOCITY_UPRIGHT = 54, -- m/s (120 mph)
	HUMAN_TERMINAL_VELOCITY_SPREAD = 40,  -- m/s (90 mph)
	HUMAN_TERMINAL_VELOCITY_PRONE = 35,   -- m/s (78 mph)

	-- ========================================================================
	-- BIOMECHANICAL INJURY THRESHOLDS
	-- ========================================================================

	-- Impact force thresholds based on medical research
	BONE_FRACTURE_THRESHOLD = 4000, -- N (Newtons) - Tibia fracture threshold
	SPINE_COMPRESSION_THRESHOLD = 3300, -- N - Vertebral compression fracture
	SKULL_FRACTURE_THRESHOLD = 5000, -- N - Skull fracture threshold

	-- Deceleration thresholds (G-forces)
	MINOR_INJURY_THRESHOLD = 15, -- G (survivable with minor injuries)
	MODERATE_INJURY_THRESHOLD = 25, -- G (moderate trauma)
	SEVERE_INJURY_THRESHOLD = 50, -- G (life-threatening)
	FATAL_THRESHOLD = 100, -- G (likely fatal)

	-- Impact duration constants
	IMPACT_DURATION_HARD = 0.05, -- seconds (concrete, metal)
	IMPACT_DURATION_MEDIUM = 0.1, -- seconds (wood, packed earth)
	IMPACT_DURATION_SOFT = 0.2,  -- seconds (sand, grass, water)
	IMPACT_DURATION_CUSHIONED = 0.3, -- seconds (Fabric)

	-- ========================================================================
	-- ENERGY AND DAMAGE CONVERSION
	-- ========================================================================

	-- Energy-to-damage conversion factors (calibrated for gameplay)
	JOULES_TO_DAMAGE_FACTOR = 0.015, -- Base conversion rate
	FORCE_TO_DAMAGE_FACTOR = 0.008,  -- Force-based damage component

	-- Damage scaling factors for different body regions
	HEAD_DAMAGE_MULTIPLIER = 1.5,  -- Head impacts are more severe
	TORSO_DAMAGE_MULTIPLIER = 1.0, -- Baseline damage
	LIMB_DAMAGE_MULTIPLIER = 0.7,  -- Limb impacts are less severe

	-- ========================================================================
	-- MATERIAL SCIENCE CONSTANTS
	-- ========================================================================

	-- Coefficient of restitution (bounciness) for materials
	RESTITUTION_COEFFICIENTS = {
		CONCRETE = 0.1,  -- Very little bounce
		METAL = 0.2,     -- Slight bounce
		WOOD = 0.3,      -- Moderate bounce
		RUBBER = 0.8,    -- High bounce
		WATER = 0.0,     -- No bounce (inelastic)
	},

	-- Material hardness values (Shore hardness scale adapted)
	MATERIAL_HARDNESS = {
		FABRIC = 5,      -- Very soft
		GRASS = 15,      -- Soft
		SAND = 25,       -- Soft-medium
		WOOD = 40,       -- Medium
		CONCRETE = 85,   -- Hard
		METAL = 95,      -- Very hard
		DIAMOND = 100,   -- Hardest
	},

	-- Material density values (kg/m³)
	MATERIAL_DENSITY = {
		AIR = 1.225,
		WATER = 1000,
		SAND = 1600,
		WOOD = 600,
		CONCRETE = 2400,
		STEEL = 7850,
	},
}

-- ============================================================================
-- CONFIGURATION
-- ============================================================================
local FallDamage_Configuration: Types.FallDamageConfig = {
	-- ========================================================================
	-- VELOCITY AND DAMAGE THRESHOLDS
	-- ========================================================================

	-- Minimum downward velocity before fall damage (based on human pain threshold)
	-- Real-world: ~3 m/s (10 ft/s) begins to cause discomfort
	MinFallVelocity = -15, -- studs/s

	-- Maximum velocity for damage scaling (human terminal velocity)
	-- Real-world: ~54 m/s terminal velocity upright
	MaxFallVelocity = -120, -- studs/s

	-- Maximum damage (represents fatal injuries)
	MaxDamage = 100,

	-- Damage curve exponent (controls damage progression)
	-- 2.0 = quadratic, 1.5 = moderate curve, 3.0 = steep curve
	DamageScalingFactor = 1.2,
	DamageCurveExponent = 2.2,

	-- ========================================================================
	-- GAMEPLAY MECHANICS
	-- ========================================================================

	-- Cooldown between damage applications (prevents spam)
	Cooldown = 1.0, -- seconds

	-- Enable advanced material physics
	UseMaterialModifiers = true,
	UseVelocityDependentMaterials = true,

	-- Special damage bonuses
	LavaBonusDamage = 50,
	IceBonusDamage = 10,

	-- ========================================================================
	-- COMPREHENSIVE MATERIAL MODIFIERS
	-- ========================================================================

	MaterialModifiers = {
		-- ====================================================================
		-- SOFT ORGANIC MATERIALS (High Energy Absorption)
		-- ====================================================================

		[Enum.Material.Grass] = 0.55, 			-- Natural cushioning, deformation absorption
		[Enum.Material.Sand] = 0.45,  			-- Excellent energy absorption through displacement
		[Enum.Material.Mud] = 0.40,   			-- Very soft, high deformation
		[Enum.Material.Snow] = 0.35,  			-- Excellent cushioning when deep
		[Enum.Material.Fabric] = 0.30, 			-- Soft textiles, high deformation

		-- ====================================================================
		-- NATURAL MATERIALS (Medium Energy Absorption)
		-- ====================================================================

		[Enum.Material.Wood] = 0.75, 			-- Moderate absorption, some give
		[Enum.Material.WoodPlanks] = 0.80,  	-- Slightly harder than raw wood
		[Enum.Material.Ground] = 0.65,     		-- Packed earth, moderate hardness
		[Enum.Material.Rock] = 0.90,       		-- Hard but with some texture
		[Enum.Material.Salt] = 0.70,       		-- Granular, some absorption

		-- ====================================================================
		-- CONSTRUCTION MATERIALS (Low Energy Absorption)
		-- ====================================================================

		[Enum.Material.Concrete] = 1.00,   		-- Baseline - very hard, minimal absorption
		[Enum.Material.Brick] = 0.95,      		-- Slightly softer than concrete
		[Enum.Material.Cobblestone] = 0.92, 	-- Textured surface, slight absorption
		[Enum.Material.Pavement] = 1.05,   		-- Asphalt, slightly harder than concrete
		[Enum.Material.Limestone] = 0.88,  		-- Natural stone, some porosity
		[Enum.Material.Granite] = 0.98,    		-- Very hard igneous rock
		[Enum.Material.Marble] = 0.96,     		-- Polished stone, minimal absorption

		-- ====================================================================
		-- METAL MATERIALS (Reflective, High Impact)
		-- ====================================================================

		[Enum.Material.Metal] = 1.15,           -- Hard, reflective surface
		[Enum.Material.CorrodedMetal] = 1.10,   -- Slightly textured
		[Enum.Material.DiamondPlate] = 1.20,    -- Textured but very hard

		-- ====================================================================
		-- GLASS AND CERAMIC MATERIALS (Brittle, High Impact)
		-- ====================================================================

		[Enum.Material.Glass] = 1.25,      		-- Brittle, shatters on impact
		[Enum.Material.SmoothPlastic] = 0.85, 	-- Synthetic, some flex
		[Enum.Material.Plastic] = 0.82,    		-- Slightly more flexible
		[Enum.Material.Neon] = 1.15,       		-- Glass-like properties
		[Enum.Material.ForceField] = 0.60, 		-- Energy-absorbing field

		-- ====================================================================
		-- ICE MATERIALS (Slippery, Brittle)
		-- ====================================================================

		[Enum.Material.Ice] = 1.18,        		-- Hard and slippery
		[Enum.Material.Glacier] = 1.22,    		-- Very hard, compressed ice

		-- ====================================================================
		-- LIQUID MATERIALS (Velocity-Dependent)
		-- ====================================================================

		[Enum.Material.Water] = 0.70,      		-- Deceptive - soft at low speed, hard at high speed
		[Enum.Material.Air] = 0.10,        		-- Gaseous, minimal resistance

		-- ====================================================================
		-- SYNTHETIC MATERIALS
		-- ====================================================================

		[Enum.Material.Rubber] = 0.45,     		-- Elastic, high energy absorption
		[Enum.Material.Pebble] = 0.78,     		-- Small stones, some movement
		[Enum.Material.Asphalt] = 1.08,    		-- Road surface, hard
		[Enum.Material.LeafyGrass] = 0.52, 		-- Thicker grass coverage
		[Enum.Material.Basalt] = 0.94,     		-- Volcanic rock, hard
		[Enum.Material.CrackedLava] = 1.30, 	-- Extremely hard, adds heat damage

		-- ====================================================================
		-- SPECIALTY MATERIALS
		-- ====================================================================

		[Enum.Material.Slate] = 0.91,      		-- Sedimentary rock, moderate hardness
		[Enum.Material.Sandstone] = 0.83,  		-- Softer stone, some absorption
		[Enum.Material.Cardboard] = 0.35,  		-- Very soft, high deformation
		[Enum.Material.Plaster] = 0.88,    		-- Brittle but softer than concrete
	},

	-- Enable debug logging and monitoring
	Debug = true,
}

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Configuration: Types.Config = {
	PHYSICS_CONSTANTS = PHYSICS_CONSTANTS,
	FallDamageConfig = FallDamage_Configuration,
}

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Configuration