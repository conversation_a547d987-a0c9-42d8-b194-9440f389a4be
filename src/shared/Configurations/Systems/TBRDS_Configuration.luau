--!strict

--[[
    - file: TBRDS_Configuration.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - TBRDS Configuration Module
      - Central configuration for the Tag-Based Role Display System (TBRDS)
      - Manages role priority and validation rules
      - Enables debug mode and performance monitoring
      - Defines security and rate limiting parameters
      - Used by both client and server components
]]

-- ============================================================================
-- TYPE ANNOTATIONS & INFER USAGE
-- ============================================================================

local Types = require(game.ReplicatedStorage.TBRDS.TBRDS_Types)
type TBRDSConfiguration = Types.TBRDSConfiguration
local TBRDSConstants = Types.TBRDSConstants

-- ============================================================================
-- DEFINE LUAU MODULE
-- ============================================================================

local TBRDSConfig = {} :: Types.TBRDSConfiguration

-- ============================================================================
-- TBRDS SYSTEM SETTINGS
-- ============================================================================

TBRDSConfig.Settings = {
  DebugMode = false,
  EnablePerformanceMetrics = true,
  EnableEventSystem = true,
  RateLimit = {
    Window = 60,
    MaxRequests = 5,
  },
  MaxTagLength = 50,
  MaxDisplayNameLength = 100,
  TagValidationInterval = 60,
  GroupRankCheckInterval = 30,
  MaxCachedPlayers = 200,
  BillboardSettings = {
    MaxDistance = 15,
    StudsOffset = Vector3.new(0, 3.5, 0),
    Size = UDim2.new(8, 0, 2, 0),
    AlwaysOnTop = true,
    LightInfluence = 1,
  },
  AntiExploit = {
    EnablePositionValidation = true,
    EnableRoleValidation = true,
    EnableDataStoreValidation = true,
    MaxRoleChangesPerMinute = 3,
    SuspiciousActivityThreshold = 10,
  },
} :: Types.TBRDSSettings

-- ============================================================================
-- GROUP CONFIGURATION
-- ============================================================================

TBRDSConfig.Groups = {
  Primary = {
    Id = TBRDSConstants.GROUP_ID,
    Name = TBRDSConstants.GROUP_NAME,
  },
}

-- ============================================================================
-- ROLE PRIORITY CONFIGURATION (highest to lowest)
-- ============================================================================

TBRDSConfig.RolePriority = TBRDSConstants.ROLE_PRIORITY

-- ============================================================================
-- GAMEPASS CONFIGURATION
-- ============================================================================

TBRDSConfig.GamePasses = TBRDSConstants.GAMEPASS_IDS

-- ============================================================================
-- EVENT NAMES
-- ============================================================================

TBRDSConfig.Events = TBRDSConstants.EVENT_NAMES

-- ============================================================================
-- REMOTE EVENT NAMES
-- ============================================================================

TBRDSConfig.Remotes = TBRDSConstants.REMOTE_NAMES

-- ============================================================================
-- ERROR CODES
-- ============================================================================

TBRDSConfig.ErrorCodes = TBRDSConstants.ERROR_CODES

-- ============================================================================
-- PERFORMANCE METRICS CONFIGURATION
-- ============================================================================

TBRDSConfig.Metrics = {
  TrackTagAssignments = true,
  TrackValidationTime = true,
  TrackSecurityEvents = true,
  TrackPerformance = true,
  MetricsRetentionTime = 3600, -- 1 hour in seconds
} :: Types.MetricsConfig

-- ============================================================================
-- DEBUG CONFIGURATION
-- ============================================================================

TBRDSConfig.Debug = {
  LogTagAssignments = true,
  LogValidationEvents = true,
  LogSecurityEvents = true,
  LogPerformanceMetrics = false,
  VerboseLogging = false,
} :: Types.DebugConfig

-- ============================================================================
-- VALIDATION RULES
-- ============================================================================

TBRDSConfig.ValidationRules = {
  RequireCharacter = true,
  RequireValidUserId = true,
  RequireGroupMembership = false, -- For default users
  ValidateRolePermissions = true,
  CheckGamePassOwnership = true,
} :: Types.ValidationRules

-- ============================================================================
-- CACHE CONFIGURATION
-- ============================================================================

TBRDSConfig.Cache = {
  PlayerDataTTL = 300, -- 5 minutes
  RoleDataTTL = 600, -- 10 minutes
  GroupDataTTL = 1800, -- 30 minutes
  MaxCacheSize = 1000,
} :: Types.CacheConfig

-- ============================================================================
-- TBRDS UTILITY FUNCTIONS
-- ============================================================================

-- Role priority retrieval
function TBRDSConfig.GetRolePriority(role: string): number?
  for index: number, roleName: string in ipairs(TBRDSConfig.RolePriority) do
    if roleName == role then
      return index
    end
  end
  return nil -- Role not found
end

-- Role validation
function TBRDSConfig.IsValidRole(role: string): boolean
  for _, roleName: string in ipairs(TBRDSConfig.RolePriority) do
    if roleName == role then
      return true
    end
  end
  return false
end

-- Debug mode utilities
function TBRDSConfig.IsDebugMode(): boolean
  return TBRDSConfig.Settings.DebugMode
end

-- Enables or disables debug mode
function TBRDSConfig.SetDebugMode(enabled: boolean): ()
  TBRDSConfig.Settings.DebugMode = enabled
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return TBRDSConfig :: TBRDSConfiguration
