--!strict

--[[
    - file: MAFS_Configuration.luau

    - version: 2.2.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Central configuration module for the MAFS (Modular Audio FootStep) system.
      - Centralized material sound definitions and properties
      - System-wide settings and performance parameters
      - Debug mode and monitoring configuration
      - Security and rate limiting parameters
      - Cross-platform compatibility (client/server)
]]

-- ============================================================================
-- MODULE DEPENDENCIES
-- ============================================================================
local Types = require(game.ReplicatedStorage.MAFS.MAFS_Types)

-- ============================================================================
-- MODULE DEFINITION
-- ============================================================================
local MAFSConfig: Types.MAFSConfigModule = {} :: Types.MAFSConfigModule

-- ============================================================================
-- MAFS SYSTEM SETTINGS
-- ============================================================================

--[[
    Core system configuration settings

    PERFORMANCE IMPACT:
    - BroadcastRadius: Higher values increase network traffic
    - MaxCachedSounds: Higher values use more memory but reduce GC pressure
    - Cooldowns: Lower values increase CPU usage but improve responsiveness

    SECURITY CONSIDERATIONS:
    - ServerCooldown: Prevents request spam and reduces server load
    - ClientCooldown: Should be slightly higher than server to prevent race conditions
    - MaxDistanceDelta: Prevents teleportation exploits
    - MovementThreshold: Prevents micro-movement spam

    AUDIO QUALITY:
    - StepInterval: Lower values create more realistic footsteps but use more resources
    - MovementThreshold: Lower values are more sensitive but may trigger false positives
]]
MAFSConfig.Settings = {
  -- Debug and monitoring settings
  DebugMode = false,
  EnablePerformanceMetrics = false, -- Track system performance metrics

  -- Performance and resource management
  MaxCachedSounds = 20, -- Maximum sounds in object pool (memory vs GC trade-off)
  BroadcastRadius = 100, -- Maximum distance for footstep broadcasting (studs)

  -- Rate limiting and security (relaxed for development/testing)
  ServerCooldown = 0.2, -- Minimum time between server validations
  ClientCooldown = 0.2, -- Minimum time between client requests
  MaxDistanceDelta = 10, -- Maximum movement distance per frame

  -- Audio timing and sensitivity
  MovementThreshold = 0.1, -- Minimum movement distance to trigger footstep (studs)
  StepInterval = 0.5, -- Minimum time between footstep sounds (seconds)
} :: Types.MAFSSettings

-- ============================================================================
-- BUILT-IN MATERIAL SOUND DEFINITIONS
-- ============================================================================

--[[
    Sound configuration for built-in Roblox materials

    AUDIO DESIGN PRINCIPLES:
    - Volume: Reflects material density and hardness (0.5-1.2 range)
    - PlaybackSpeedRange: Adds natural variation (typically ±10-20%)
    - RollOffDistance: Matches real-world sound propagation characteristics

    PERFORMANCE NOTES:
    - Multiple SoundIds provide variation and prevent repetition
    - RollOff distances are optimized for typical game environments
    - Volume levels are balanced for consistent player experience

    CUSTOMIZATION:
    - Add more SoundIds for greater variation
    - Adjust Volume for game-specific balance
    - Modify RollOff distances based on map scale
]]
MAFSConfig.MaterialSounds = {
  -- Natural materials
  [Enum.Material.Grass] = {
    SoundIds = {
      "rbxassetid://72769632775790", -- Soft grass step 1
      "rbxassetid://72769632775790", -- Soft grass step 2
      "rbxassetid://72769632775790", -- Soft grass step 3
    },
    Volume = 0.8, -- Moderate volume for natural feel
    PlaybackSpeedRange = { 0.9, 1.1 }, -- ±10% speed variation
    RollOffMinDistance = 5, -- Close proximity for intimate sound
    RollOffMaxDistance = 50, -- Standard outdoor range
  } :: Types.MaterialData,

  [Enum.Material.Sand] = {
    SoundIds = {
      "rbxassetid://9118663775", -- Sand crunch 1
      "rbxassetid://9118664041", -- Sand crunch 2
      "rbxassetid://9118664270", -- Sand crunch 3
    },
    Volume = 0.7, -- Softer than grass due to absorption
    PlaybackSpeedRange = { 0.9, 1.1 }, -- Natural variation
    RollOffMinDistance = 4, -- Closer due to muffled nature
    RollOffMaxDistance = 45, -- Slightly reduced range
  } :: Types.MaterialData,

  -- Hard surfaces
  [Enum.Material.Concrete] = {
    SoundIds = {
      "rbxassetid://9118662067", -- Hard concrete step 1
      "rbxassetid://9118662323", -- Hard concrete step 2
      "rbxassetid://9118662536", -- Hard concrete step 3
    },
    Volume = 1.0, -- Standard reference volume
    PlaybackSpeedRange = { 0.95, 1.05 }, -- Tighter range for consistent feel
    RollOffMinDistance = 6, -- Slightly further due to hardness
    RollOffMaxDistance = 55, -- Good propagation on hard surface
  } :: Types.MaterialData,

  [Enum.Material.Wood] = {
    SoundIds = {
      "rbxassetid://9118664500", -- Wood creak 1
      "rbxassetid://9118664650", -- Wood creak 2
      "rbxassetid://9118664800", -- Wood creak 3
    },
    Volume = 0.9, -- Slightly softer than concrete
    PlaybackSpeedRange = { 0.9, 1.1 }, -- Natural wood variation
    RollOffMinDistance = 5, -- Moderate proximity
    RollOffMaxDistance = 50, -- Standard indoor/outdoor range
  } :: Types.MaterialData,

  -- Metallic materials
  [Enum.Material.Metal] = {
    SoundIds = {
      "rbxassetid://9118665000", -- Metal clang 1
      "rbxassetid://9118665150", -- Metal clang 2
      "rbxassetid://9118665300", -- Metal clang 3
    },
    Volume = 1.1, -- Louder due to resonance
    PlaybackSpeedRange = { 0.95, 1.05 }, -- Tighter for metallic consistency
    RollOffMinDistance = 7, -- Further due to sharp sound
    RollOffMaxDistance = 60, -- Excellent propagation
  } :: Types.MaterialData,

  [Enum.Material.Rock] = {
    SoundIds = {
      "rbxassetid://9118665500", -- Rock scrape 1
      "rbxassetid://9118665650", -- Rock scrape 2
      "rbxassetid://9118665800", -- Rock scrape 3
    },
    Volume = 1.0, -- Standard hard surface volume
    PlaybackSpeedRange = { 0.9, 1.1 }, -- Natural stone variation
    RollOffMinDistance = 6, -- Similar to concrete
    RollOffMaxDistance = 55, -- Good outdoor propagation
  } :: Types.MaterialData,

  -- Special materials
  [Enum.Material.Water] = {
    SoundIds = {
      "rbxassetid://9118666000", -- Water splash 1
      "rbxassetid://9118666150", -- Water splash 2
      "rbxassetid://9118666300", -- Water splash 3
    },
    Volume = 0.6, -- Quieter due to absorption
    PlaybackSpeedRange = { 0.8, 1.2 }, -- Wider range for fluid dynamics
    RollOffMinDistance = 4, -- Close due to muffled nature
    RollOffMaxDistance = 40, -- Reduced range in water
  } :: Types.MaterialData,

  -- Fabric
  [Enum.Material.Fabric] = {
    SoundIds = {
      "rbxassetid://9118667000", -- Fabric step 1
      "rbxassetid://9118667150", -- Fabric step 2
      "rbxassetid://9118667300", -- Fabric step 3
    },
    Volume = 0.7, -- Softer volume for fabric materials
    PlaybackSpeedRange = { 0.9, 1.1 }, -- Natural variation
    RollOffMinDistance = 4, -- Close proximity for fabric sounds
    RollOffMaxDistance = 40, -- Reduced range for intimate sound
  } :: Types.MaterialData,

  -- Glass
  [Enum.Material.Glass] = {
    SoundIds = {
      "rbxassetid://9118668000", -- Glass step 1
      "rbxassetid://9118668150", -- Glass step 2
      "rbxassetid://9118668300", -- Glass step 3
    },
    Volume = 1.0, -- Standard volume for glass
    PlaybackSpeedRange = { 0.95, 1.05 }, -- Tighter range for consistency
    RollOffMinDistance = 6, -- Slightly further due to material properties
    RollOffMaxDistance = 55, -- Good propagation for glass surfaces
  } :: Types.MaterialData,

  -- Marble
  [Enum.Material.Marble] = {
    SoundIds = {
      "rbxassetid://9118668500", -- Marble step 1
      "rbxassetid://9118668650", -- Marble step 2
      "rbxassetid://9118668800", -- Marble step 3
    },
    Volume = 1.0, -- Standard volume for marble
    PlaybackSpeedRange = { 0.95, 1.05 }, -- Tighter range for consistency
    RollOffMinDistance = 6, -- Slightly further due to material properties
    RollOffMaxDistance = 55, -- Good propagation for marble surfaces
  } :: Types.MaterialData,

  -- Neon
  [Enum.Material.Neon] = {
    SoundIds = {
      "rbxassetid://9118669000", -- Neon step 1
      "rbxassetid://9118669150", -- Neon step 2
      "rbxassetid://9118669300", -- Neon step 3
    },
    Volume = 0.7, -- Quieter volume for neon
    PlaybackSpeedRange = { 0.95, 1.05 }, -- Tighter range for consistency
    RollOffMinDistance = 4, -- Close proximity for neon lights
    RollOffMaxDistance = 40, -- Reduced range for intimate sound
  } :: Types.MaterialData,

  -- Plastic
  [Enum.Material.Plastic] = {
    SoundIds = {
      "rbxassetid://9118669500", -- Plastic step 1
      "rbxassetid://9118669650", -- Plastic step 2
      "rbxassetid://9118669800", -- Plastic step 3
    },
    Volume = 0.8, -- Moderate volume for plastic
    PlaybackSpeedRange = { 0.9, 1.1 }, -- Natural variation
    RollOffMinDistance = 5, -- Standard proximity for plastic
    RollOffMaxDistance = 50, -- Standard range for plastic materials
  } :: Types.MaterialData,

  -- Slate
  [Enum.Material.Slate] = {
    SoundIds = {
      "rbxassetid://9118670000", -- Slate step 1
      "rbxassetid://9118670150", -- Slate step 2
      "rbxassetid://9118670300", -- Slate step 3
    },
    Volume = 1.0, -- Standard volume for slate
    PlaybackSpeedRange = { 0.9, 1.1 }, -- Natural variation
    RollOffMinDistance = 6, -- Slightly further due to material properties
    RollOffMaxDistance = 55, -- Good propagation for slate surfaces
  } :: Types.MaterialData,

  -- Wood
  [Enum.Material.Wood] = {
    SoundIds = {
      "rbxassetid://9118670500", -- Wood step 1
      "rbxassetid://9118670650", -- Wood step 2
      "rbxassetid://9118670800", -- Wood step 3
    },
    Volume = 0.9, -- Slightly softer than concrete
    PlaybackSpeedRange = { 0.9, 1.1 }, -- Natural wood variation
    RollOffMinDistance = 5, -- Moderate proximity
    RollOffMaxDistance = 50, -- Standard indoor/outdoor range
  } :: Types.MaterialData,
} :: Types.MaterialSoundsMap

-- ============================================================================
-- CUSTOM MATERIAL SOUND DEFINITIONS
-- ============================================================================

--[[
    Custom materials accessed via FootstepMaterial attribute on parts

    USAGE:
    part:SetAttribute("FootstepMaterial", "Snow")

    NAMING CONVENTIONS:
    - Use PascalCase for consistency (e.g., "MetalGrate", not "metal_grate")
    - Names should be descriptive and unique
    - Avoid conflicts with built-in material names

    IMPLEMENTATION NOTES:
    - Custom materials override built-in material detection
    - Attribute-based system allows per-part customization
    - Supports hierarchical material assignment (model-wide)

    EXTENSIBILITY:
    - Add new materials by following the existing pattern
    - Ensure all required fields are present
    - Test audio balance with existing materials
]]
MAFSConfig.CustomMaterials = {
  -- Asphalt
  ["Asphalt"] = {
    SoundIds = {
      "rbxassetid://277067660",
    },
    Volume = 0.85,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  } :: Types.MaterialData,

  -- Basalt
  ["Basalt"] = {
    SoundIds = {
      "rbxassetid://1234567890", -- Placeholder
    },
    Volume = 0.95,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  } :: Types.MaterialData,

  -- Brick
  ["Brick"] = {
    SoundIds = {
      "rbxassetid://1234567891", -- Placeholder
    },
    Volume = 1.0,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  } :: Types.MaterialData,

  -- Cobblestone
  ["Cobblestone"] = {
    SoundIds = {
      "rbxassetid://1234567892", -- Placeholder
    },
    Volume = 1.0,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  } :: Types.MaterialData,

  -- Concrete
  ["Concrete"] = {
    SoundIds = {
      "rbxassetid://9118662067",
      "rbxassetid://9118662323",
      "rbxassetid://9118662536",
    },
    Volume = 1.0,
    PlaybackSpeedRange = { 0.95, 1.05 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  } :: Types.MaterialData,

  -- CorrodedMetal
  ["CorrodedMetal"] = {
    SoundIds = {
      "rbxassetid://1234567893", -- Placeholder
    },
    Volume = 1.1,
    PlaybackSpeedRange = { 0.95, 1.05 },
    RollOffMinDistance = 7,
    RollOffMaxDistance = 60,
  } :: Types.MaterialData,

  -- Fabric
  ["Fabric"] = {
    SoundIds = {
      "rbxassetid://1234567894", -- Placeholder
    },
    Volume = 0.7,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 4,
    RollOffMaxDistance = 40,
  } :: Types.MaterialData,

  -- Foil
  ["Foil"] = {
    SoundIds = {
      "rbxassetid://1234567895", -- Placeholder
    },
    Volume = 0.8,
    PlaybackSpeedRange = { 0.95, 1.05 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  } :: Types.MaterialData,

  -- ForceField
  ["ForceField"] = {
    SoundIds = {
      "rbxassetid://1234567896", -- Placeholder
    },
    Volume = 0.6,
    PlaybackSpeedRange = { 0.95, 1.05 },
    RollOffMinDistance = 4,
    RollOffMaxDistance = 40,
  } :: Types.MaterialData,

  -- Glacier
  ["Glacier"] = {
    SoundIds = {
      "rbxassetid://1234567897", -- Placeholder
    },
    Volume = 0.7,
    PlaybackSpeedRange = { 0.85, 1.05 },
    RollOffMinDistance = 4,
    RollOffMaxDistance = 40,
  } :: Types.MaterialData,

  -- Glass
  ["Glass"] = {
    SoundIds = {
      "rbxassetid://1234567898", -- Placeholder
    },
    Volume = 1.0,
    PlaybackSpeedRange = { 0.95, 1.05 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  } :: Types.MaterialData,

  -- Granite
  ["Granite"] = {
    SoundIds = {
      "rbxassetid://1234567899", -- Placeholder
    },
    Volume = 1.0,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  } :: Types.MaterialData,

  -- Grass
  ["Grass"] = {
    SoundIds = {
      "rbxassetid://72769632775790",
    },
    Volume = 0.8,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  } :: Types.MaterialData,

  -- Leafy Grass
  ["Leafy Grass"] = {
    SoundIds = {
      "rbxassetid://1234567900", -- Placeholder
    },
    Volume = 0.8,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  } :: Types.MaterialData,

  -- Marble
  ["Marble"] = {
    SoundIds = {
      "rbxassetid://1234567901", -- Placeholder
    },
    Volume = 1.0,
    PlaybackSpeedRange = { 0.95, 1.05 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  } :: Types.MaterialData,

  -- Metal
  ["Metal"] = {
    SoundIds = {
      "rbxassetid://9118665000",
      "rbxassetid://9118665150",
      "rbxassetid://9118665300",
    },
    Volume = 1.1,
    PlaybackSpeedRange = { 0.95, 1.05 },
    RollOffMinDistance = 7,
    RollOffMaxDistance = 60,
  } :: Types.MaterialData,

  -- Mud
  ["Mud"] = {
    SoundIds = {
      "rbxassetid://9118667500",
      "rbxassetid://9118667650",
      "rbxassetid://9118667800",
    },
    Volume = 0.7,
    PlaybackSpeedRange = { 0.8, 1.0 },
    RollOffMinDistance = 4,
    RollOffMaxDistance = 40,
  } :: Types.MaterialData,

  -- Neon
  ["Neon"] = {
    SoundIds = {
      "rbxassetid://1234567902", -- Placeholder
    },
    Volume = 0.7,
    PlaybackSpeedRange = { 0.95, 1.05 },
    RollOffMinDistance = 4,
    RollOffMaxDistance = 40,
  } :: Types.MaterialData,

  -- Pavement
  ["Pavement"] = {
    SoundIds = {
      "rbxassetid://1234567903", -- Placeholder
    },
    Volume = 0.95,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  } :: Types.MaterialData,

  -- Pebble
  ["Pebble"] = {
    SoundIds = {
      "rbxassetid://1234567904", -- Placeholder
    },
    Volume = 0.9,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  } :: Types.MaterialData,

  -- Plastic
  ["Plastic"] = {
    SoundIds = {
      "rbxassetid://1234567905", -- Placeholder
    },
    Volume = 0.8,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  } :: Types.MaterialData,

  -- Rock
  ["Rock"] = {
    SoundIds = {
      "rbxassetid://9118665500",
      "rbxassetid://9118665650",
      "rbxassetid://9118665800",
    },
    Volume = 1.0,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  } :: Types.MaterialData,

  -- Salt
  ["Salt"] = {
    SoundIds = {
      "rbxassetid://1234567906", -- Placeholder
    },
    Volume = 0.7,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 4,
    RollOffMaxDistance = 40,
  } :: Types.MaterialData,

  -- Sand
  ["Sand"] = {
    SoundIds = {
      "rbxassetid://9118663775",
      "rbxassetid://9118664041",
      "rbxassetid://9118664270",
    },
    Volume = 0.7,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 4,
    RollOffMaxDistance = 45,
  } :: Types.MaterialData,

  -- Sandstone
  ["Sandstone"] = {
    SoundIds = {
      "rbxassetid://1234567907", -- Placeholder
    },
    Volume = 0.8,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  } :: Types.MaterialData,

  -- Slate
  ["Slate"] = {
    SoundIds = {
      "rbxassetid://1234567908", -- Placeholder
    },
    Volume = 1.0,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 6,
    RollOffMaxDistance = 55,
  } :: Types.MaterialData,

  -- SmoothPlastic
  ["SmoothPlastic"] = {
    SoundIds = {
      "rbxassetid://1234567909", -- Placeholder
    },
    Volume = 0.8,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  } :: Types.MaterialData,

  -- Snow
  ["Snow"] = {
    SoundIds = {
      "rbxassetid://9118666500",
      "rbxassetid://9118666650",
      "rbxassetid://9118666800",
    },
    Volume = 0.5,
    PlaybackSpeedRange = { 0.8, 1.0 },
    RollOffMinDistance = 3,
    RollOffMaxDistance = 35,
  } :: Types.MaterialData,

  -- Wood
  ["Wood"] = {
    SoundIds = {
      "rbxassetid://9118664500",
      "rbxassetid://9118664650",
      "rbxassetid://9118664800",
    },
    Volume = 0.9,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  } :: Types.MaterialData,

  -- WoodPlanks
  ["WoodPlanks"] = {
    SoundIds = {
      "rbxassetid://1234567910", -- Placeholder
    },
    Volume = 0.9,
    PlaybackSpeedRange = { 0.9, 1.1 },
    RollOffMinDistance = 5,
    RollOffMaxDistance = 50,
  } :: Types.MaterialData,
} :: Types.CustomMaterialsMap

-- ============================================================================
-- DEFAULT MATERIAL CONFIGURATION
-- ============================================================================

--[[
    Fallback material used when no specific material is found

    DESIGN RATIONALE:
    - Uses concrete sounds as a neutral, common baseline
    - Moderate volume and standard rolloff for general compatibility
    - Conservative settings to avoid audio issues

    USAGE:
    - Automatically used when material resolution fails
    - Serves as a safe fallback for unknown materials
    - Should represent the most common surface type in your game
]]
MAFSConfig.DefaultMaterial = {
  SoundIds = {
    "rbxassetid://6563562192", -- Generic step
  },
  Volume = 0.25, -- Safe moderate volume
  PlaybackSpeedRange = { 0.9, 1.1 }, -- Standard variation
  RollOffMinDistance = 5, -- Standard proximity
  RollOffMaxDistance = 50, -- Standard range
} :: Types.MaterialData

-- ============================================================================
-- CONSTANTS
-- ============================================================================

local CONSTANTS = {
  MIN_VOLUME = 0,
  MAX_VOLUME = 2,
  PLAYBACK_SPEED_RANGE_SIZE = 2,
  MIN_SOUND_IDS = 1,
} :: {
  MIN_VOLUME: number,
  MAX_VOLUME: number,
  PLAYBACK_SPEED_RANGE_SIZE: number,
  MIN_SOUND_IDS: number,
}

-- ============================================================================
-- CORE UTILITY FUNCTIONS
-- ============================================================================

--[[
    Retrieves material data with hierarchical fallback system

    RESOLUTION ORDER:
    1. Built-in Roblox material (if Enum.Material provided)
    2. Custom material (if string provided and exists)
    3. Default material (fallback)

    PARAMETERS:
    material: Types.MaterialIdentifier - Either Enum.Material or string

    RETURNS:
    Types.MaterialData - Complete material configuration

    PERFORMANCE:
    - O(1) hash table lookups
    - No iteration or complex logic
    - Optimized for frequent calls during gameplay
]]
function MAFSConfig.GetMaterialData(material: Types.MaterialIdentifier): Types.MaterialData
  print(`The material that is in MAFSConfig.GetMaterialData() is: {material}`)

  -- Type guard for material parameter
  if material == nil then
    warn("MAFS: GetMaterialData received nil material, using default")
    return MAFSConfig.DefaultMaterial
  end

  -- Check for built-in Roblox material first
  if
    typeof(material) == "EnumItem"
    and material:IsA("Enum.Material")
    and MAFSConfig.MaterialSounds[material]
  then
    return MAFSConfig.MaterialSounds[material]
  end

  -- Check for custom material by name
  if type(material) == "string" and MAFSConfig.CustomMaterials[material] then
    return MAFSConfig.CustomMaterials[material]
  end

  -- Fallback to default material
  return MAFSConfig.DefaultMaterial
end

--[[
    Selects a random sound ID from the material's sound array

    PARAMETERS:
    materialData: Types.MaterialData - Material configuration containing SoundIds

    RETURNS:
    string - Random sound ID from the array

    USAGE:
    Provides audio variation by randomly selecting from available sounds
]]
function MAFSConfig.GetRandomSoundId(materialData: Types.MaterialData): string
  if not materialData or not materialData.SoundIds or #materialData.SoundIds == 0 then
    warn("MAFS: Invalid material data provided to GetRandomSoundId")
    return MAFSConfig.DefaultMaterial.SoundIds[1]
  end

  local soundIds = materialData.SoundIds
  local random = soundIds[math.random(1, #soundIds)]
  print(`Server side Material Sound Ids: {random}`)
  return random
end

--[[
    Generates a random playback speed within the material's defined range

    PARAMETERS:
    materialData: Types.MaterialData - Material configuration with PlaybackSpeedRange

    RETURNS:
    number - Random speed between min and max range values

    USAGE:
    Adds natural variation to footstep audio timing
]]
function MAFSConfig.GetRandomPlaybackSpeed(materialData: Types.MaterialData): number
  -- Range
  local range = materialData.PlaybackSpeedRange

  -- If no playback speed range size isn't provided, return warning
  if not range or #range ~= CONSTANTS.PLAYBACK_SPEED_RANGE_SIZE then
    warn(`MAFS: Invalid playback speed range for material`)
    return 1 -- or some default
  end

  return range[1] + math.random() * (range[2] - range[1])
end

--[[
    Checks if debug mode is currently enabled

    RETURNS:
    boolean - Current debug mode state

    USAGE:
    Used throughout the system for conditional debug logging
]]
function MAFSConfig.IsDebugMode()
  return MAFSConfig.Settings.DebugMode
end

--[[
    Enables or disables debug mode at runtime

    PARAMETERS:
    enabled: boolean - New debug mode state

    USAGE:
    Allows dynamic toggling of debug features during development
]]
function MAFSConfig.SetDebugMode(enabled)
  MAFSConfig.Settings.DebugMode = enabled
end

--[[
    Validates the entire configuration for consistency and correctness

    RETURNS:
    boolean - True if configuration is valid, false otherwise

    VALIDATION CHECKS:
    - All required fields are present
    - Volume levels are within reasonable ranges
    - Sound IDs are properly formatted
    - Rolloff distances are logical
    - No circular references or conflicts

    USAGE:
    Called during system initialization to ensure configuration integrity
]]
function MAFSConfig.ValidateConfiguration()
  -- Validate settings
  local settings = MAFSConfig.Settings
  if not settings then
    warn("MAFS: Settings table is missing")
    return false
  end

  -- Check critical settings
  if
    settings.BroadcastRadius <= 0
    or settings.ServerCooldown <= 0
    or settings.ClientCooldown <= 0
  then
    warn("MAFS: Invalid settings values detected")
    return false
  end

  -- Validate material sounds
  for material, data in pairs(MAFSConfig.MaterialSounds) do
    if not MAFSConfig.ValidateMaterialData(data, tostring(material)) then
      return false
    end
  end

  -- Validate custom materials
  for name, data in pairs(MAFSConfig.CustomMaterials) do
    if not MAFSConfig.ValidateMaterialData(data, name) then
      return false
    end
  end

  -- Validate default material
  if not MAFSConfig.ValidateMaterialData(MAFSConfig.DefaultMaterial, "DefaultMaterial") then
    return false
  end

  return true
end

--[[
    Validates a single MaterialData structure

    PARAMETERS:
    data: Types.MaterialData - Material data to validate
    name: string - Name for error reporting

    RETURNS:
    boolean - True if valid, false otherwise
]]
function MAFSConfig.ValidateMaterialData(data: Types.MaterialData, name: string): boolean
  -- Check sound IDs
  if not data.SoundIds or #data.SoundIds < CONSTANTS.MIN_SOUND_IDS then
    warn(`MAFS: Material '{name}' has insufficient sound IDs`)
    return false
  end

  -- Check volume
  if data.Volume < CONSTANTS.MIN_VOLUME or data.Volume > CONSTANTS.MAX_VOLUME then
    warn(`MAFS: Material '{name}' has invalid volume: {data.Volume}`)
    return false
  end

  -- Check playback speed range
  if
    not data.PlaybackSpeedRange or #data.PlaybackSpeedRange ~= CONSTANTS.PLAYBACK_SPEED_RANGE_SIZE
  then
    warn(`MAFS: Material '{name}' has invalid playback speed range`)
    return false
  end

  -- Check rolloff distances
  if data.RollOffMinDistance >= data.RollOffMaxDistance then
    warn(`MAFS: Material '{name}' has invalid rolloff distances`)
    return false
  end

  -- All checks passed
  return true
end

-- ============================================================================
-- PUBLIC API
-- =========================================================================
--[[
    Returns a list of all available Material names

    RETURNS:
    { string } - Array of all available material identifiers

    INCLUDES:
    - Built-in Roblox material names
    - Custom material names
    - "Default" for the fallback material material"

    USAGE:
    Useful for UI generation, debugging, and validation
]]
function MAFSConfig.GetAvailableMaterials(): { string }
  local materials: { string } = {}

  -- Add built-in materials
  for material in pairs(MAFSConfig.MaterialSounds) do
    table.insert(materials, tostring(material))
  end

  -- Add custom materials
  for name in pairs(MAFSConfig.CustomMaterials) do
    table.insert(materials, name)
  end

  return materials
end

-- ===========================================================================
-- MODULE EXPORT
-- ===========================================================================
return MAFSConfig :: Types.MAFSConfigModule
