--!strict

--[[
    - file: CGS_Utils.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Centralized utility functions for the CGS (Core Game System)
      - Used for reusability across multiple CGS modules

    - Features:
      - Logging
      - Mathematical Utilities
      - Vector clamping
      - Utils for Observation Sub-system

    - dependencies:
      - CGS_Configuration
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players") :: Players
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration =
  require(ReplicatedStorage.Configurations.Systems:WaitForChild("CGS_Configuration"))

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local CGS_Utils = {}

-- ============================================================================
-- LOGGING UTILITIES
-- ============================================================================

--[[
    Formats a log tag consistently

    @param tag string - Module or system name (e.g. "CameraSwing")

    @return string - Formatted log prefix
]]
function CGS_Utils.formatTag(tag: string): string
  return "[CGS::" .. tag .. "]"
end

--[[
    Logs a message if DebugMode is enabled

    @param tag string - Source of the log

    @param ... any - Values to print
]]
function CGS_Utils.log(tag: string, ...): ()
  if Configuration.Config.General.DebugMode ~= false then
    print(CGS_Utils.formatTag(tag), ...)
  end
end

--[[
    Logs a warning message if DebugMode is enabled

    @param tag string - Source of the

    @param ... any - Values to warn
]]
function CGS_Utils.warn(tag: string, ...): ()
  if Configuration.Config.General.DebugMode ~= false then
    warn(CGS_Utils.formatTag(tag), ...)
  end
end

-- ============================================================================
-- MATHEMATICAL UTILITIES
-- ============================================================================

--[[
    Clamps a Vector2 to a maximum magnitude in both X and Y components

    @param vec The Vector2 to clamp
    @param max The maximum allowed value for both components

    @return Vector2 The clamped vector
]]
function CGS_Utils.clampVector2(vec: Vector2, max: number): Vector2
  return Vector2.new(math.clamp(vec.X, -max, max), math.clamp(vec.Y, -max, max))
end

--[[
    Clamps a Vector3 to a maximum magnitude in X, Y, and Z components

    @param vec The Vector3 to clamp
    @param max The maximum allowed value for all components

    @return Vector3 The clamped vector
]]
function CGS_Utils.clampVector3(vec: Vector3, max: number): Vector3
  return Vector3.new(
    math.clamp(vec.X, -max, max),
    math.clamp(vec.Y, -max, max),
    math.clamp(vec.Z, -max, max)
  )
end

--[[
    Gets the rig type of the given character

    @param char The character to check

    @return One of the following values: "R15" | "R6" The rig type of the character
]]
function CGS_Utils.GetRigType(char: Model): "R15" | "R6"
  local hum = char:FindFirstChild("Humanoid") :: Humanoid?
  if hum and hum.RigType == Enum.HumanoidRigType.R15 then
    return "R15"
  end
  return "R6"
end

-- ============================================================================
-- OBSERVATION SUB-SYSTEM UTILS
-- ============================================================================

--[[
    Linearly interpolates between two numbers.

    @param a number - Start value
    @param b number - End value
    @param t number - Interpolation factor between 0 and 1

    @return number - Interpolated result
]]
function CGS_Utils.lerp(a: number, b: number, t: number): number
  return a + (b - a) * t
end

--[[
    Smoothly interpolates a value using a smoother step function.

    @param t number - Interpolation factor between 0 and 1

    @return number - Smoothed value
]]
function CGS_Utils.smootherStep(t: number): number
  local x = math.clamp(t, 0, 1)
  return x * x * (3 - 2 * x)
end

--[[
    Refreshes and sorts the list of active effects by priority.

    @param activeEffects table - Dictionary of effect name to effect object
    @param sortedEffects table - Array of effects to sort into
]]
function CGS_Utils.refreshSortedEffects(
  activeEffects: { [string]: any },
  sortedEffects: { any }
): ()
  table.clear(sortedEffects)
  for _, effect in activeEffects do
    table.insert(sortedEffects, effect)
  end
  table.sort(sortedEffects, function(a, b)
    return a.Priority < b.Priority
  end)
end

--[[
    Adds a new camera effect to the system.

    @param name string - Name of the effect
    @param effect any - Effect instance
    @param activeEffects table - Dictionary of active effects
    @param sortedEffects table - Array to hold sorted effects
    @param TAG string - Logging tag for context
]]
function CGS_Utils.AddEffect(
  name: string,
  effect: any,
  activeEffects: { [string]: any },
  sortedEffects: { any },
  TAG: string
): ()
  activeEffects[name] = effect
  if effect.Init then
    effect:Init()
  end
  CGS_Utils.refreshSortedEffects(activeEffects, sortedEffects)
  if not effect.Reset then
    CGS_Utils.warn(TAG, `Effect "{name}" missing Reset() method`)
  end
end

--[[
    Removes a camera effect by name.

    @param name string - Name of the effect to remove
    @param activeEffects table - Dictionary of active effects
    @param sortedEffects table - Array of sorted effects to update
]]
function CGS_Utils.RemoveEffect(
  name: string,
  activeEffects: { [string]: any },
  sortedEffects: { any }
): ()
  activeEffects[name] = nil
  CGS_Utils.refreshSortedEffects(activeEffects, sortedEffects)
end

--[[
    Gets the current camera from the workspace.

    @return Camera? - The current camera or nil if not available
]]
function CGS_Utils.getCurrentCamera(): Camera?
  return workspace.CurrentCamera
end

--[[
    Gets the local player from the Players service.

    @return Player - The local player
]]
function CGS_Utils.getLocalPlayer(): Player
  return assert(Players.LocalPlayer, "LocalPlayer is not available")
end

--[[
    Gets the character of the local player.

    @return Model? - The local player's character or nil if not available
]]
function CGS_Utils.getHumanoid(character: Model?): Humanoid?
  return character and character:FindFirstChildOfClass("Humanoid")
end

-- ============================================================================
-- RETURN MODULE
-- ============================================================================
return CGS_Utils
