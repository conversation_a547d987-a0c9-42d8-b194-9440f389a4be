--!strict

--[[
    - file: CGS_Movement_Core.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Movement Core for CGS -> Movement Sub-system
      - Each stance has it's own speed.
      - Stances:
        - Idle
        - Walking
        - Running/Sprinting
        - Crouching
        - Proning
        - Jumping

    - dependencies:
      - CGS_Types
      - CGS_Configuration
      - CGS_Utils
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players") :: Players
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration =
  require(ReplicatedStorage.Configurations.Systems:WaitForChild("CGS_Configuration"))
local Types = require(ReplicatedStorage.CGS.CGS_Types)
local Utils = require(ReplicatedStorage.CGS.Shared:WaitForChild("CGS_Utils"))

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Config: Types.MovementConfig = Configuration.Config.Movement

local MovementCore = {}

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local TAG: string = "Movement_Core"
local player = Players.LocalPlayer :: Player
local cgsFolder = player:WaitForChild("CGS")
local movementFolder = cgsFolder:WaitForChild("Movement")

local staminaValue = movementFolder:WaitForChild("Stamina") :: NumberValue
local exhaustedValue = movementFolder:WaitForChild("Exhausted") :: BoolValue
local jumpRequest = ReplicatedStorage.CGS.Remotes:WaitForChild("jumpRequest") :: RemoteEvent

-- ============================================================================
-- FUNCTIONS
-- ============================================================================

--[[
    Creates the default movement data record.

    @return Types.MovementData The initial movement data table
]]
function MovementCore.CreateMovementData(): Types.MovementData
  Utils.log(TAG, "Movement data created")
  return {
    State = "Idle",
    Velocity = Vector3.zero,
    Stamina = staminaValue.Value,
    LastJumpTime = 0,
    InputDirection = Vector3.zero,
    IsGrounded = true,
    IsExhausted = exhaustedValue.Value,
  }
end

--[[
    Returns the movement speed for a given state.

    @param state Types.MovementState The current movement state
    @return number The speed in studs per second
]]
function MovementCore.GetStateSpeed(state: Types.MovementState): number
  local speedMap: { [Types.MovementState]: number } = {
    Idle = Config.IdleSpeed,
    Walk = Config.WalkSpeed,
    Sprint = Config.SprintSpeed,
    Crouch = Config.CrouchSpeed,
    Prone = Config.ProneSpeed,
    Jump = Config.WalkSpeed,
  }
  return speedMap[state] or Config.WalkSpeed
end

--[[
    Calculates a directional speed modifier based on input direction.

    @param inputDirection Vector3 The player's input movement vector (X and Z axes)
    @return number The speed multiplier for the current movement direction
]]
function MovementCore.GetDirectionalModifier(inputDirection: Vector3): number
  local absX: number = math.abs(inputDirection.X)
  local absZ: number = math.abs(inputDirection.Z)

  if inputDirection.Z < -0.5 then
    return 1.0
  end
  if inputDirection.Z > 0.5 then
    return Config.BackwardSpeedMultiplier
  end
  if absX > 0.5 and absZ < 0.5 then
    return Config.StrafeSpeedMultiplier
  end
  if absX > 0.5 and absZ > 0.5 then
    return if inputDirection.Z < 0 then 0.85 else Config.BackwardSpeedMultiplier * 0.8
  end
  return 1.0
end

--[[
    Determines the player's movement state based on input, stamina, and timing.

    @param inputData Types.InputData The current input snapshot
    @param movementData Types.MovementData The current movement data
    @param currentTime number The current tick time
    @return Types.MovementState The next movement state
]]
function MovementCore.DetermineState(
  inputData: Types.InputData,
  movementData: Types.MovementData,
  currentTime: number
): Types.MovementState
  local moveVector: Vector3 = inputData.MoveVector
  if moveVector.Magnitude < 0.1 then
    return "Idle"
  end

  if inputData.ProneHeld then
    return "Prone"
  end
  if inputData.CrouchHeld then
    return "Crouch"
  end

  if
    inputData.JumpPressed
    and movementData.IsGrounded
    and movementData.Stamina >= Config.JumpStaminaCost
    and currentTime - movementData.LastJumpTime >= Config.JumpCooldown
  then
    return "Jump"
  end

  if inputData.SprintHeld and movementData.Stamina > 0 and not movementData.IsExhausted then
    return "Sprint"
  end

  return "Walk"
end

--[[
    Calculates the target velocity vector based on input, state, and camera orientation.

    @param inputDirection Vector3 The movement input vector
    @param state Types.MovementState The current movement state
    @param cameraCFrame CFrame The current camera CFrame for direction conversion
    @return Vector3 The desired world space velocity vector
]]
function MovementCore.CalculateTargetVelocity(
  inputDirection: Vector3,
  state: Types.MovementState,
  cameraCFrame: CFrame
): Vector3
  if state == "Idle" then
    return Vector3.zero
  end

  local baseSpeed: number = MovementCore.GetStateSpeed(state)
  local directionModifier: number = MovementCore.GetDirectionalModifier(inputDirection)
  local finalSpeed: number = baseSpeed * directionModifier

  local worldDirection: Vector3 = cameraCFrame:VectorToWorldSpace(inputDirection)
  local horizontalDirection: Vector3 = Vector3.new(worldDirection.X, 0, worldDirection.Z)
  local unitDirection: Vector3 = if horizontalDirection.Magnitude > 0
    then horizontalDirection.Unit
    else Vector3.zero

  return unitDirection * finalSpeed
end

--[[
    Smoothly interpolates velocity towards target velocity using exponential easing.

    @param currentVelocity Vector3 The current velocity vector
    @param targetVelocity Vector3 The target velocity vector
    @param deltaTime number Time elapsed since last update
    @return Vector3 The interpolated velocity vector
]]
function MovementCore.InterpolateVelocity(
  currentVelocity: Vector3,
  targetVelocity: Vector3,
  deltaTime: number
): Vector3
  local speed: number = if targetVelocity.Magnitude > 0.1
    then Config.Acceleration
    else Config.Deceleration
  local alpha: number = 1 - math.exp(-speed * deltaTime)
  return currentVelocity:Lerp(targetVelocity, alpha)
end

--[[
    Checks whether the character is currently grounded by raycasting downward from HumanoidRootPart.

    @param character Model The player's character model
    @return boolean True if grounded, false otherwise


]]
function MovementCore.CheckGrounded(character: Model): boolean
  local humanoid: Humanoid? = character:FindFirstChildOfClass("Humanoid")
  if not humanoid then
    return false
  end

  local state = humanoid:GetState()
  return state ~= Enum.HumanoidStateType.Freefall and state ~= Enum.HumanoidStateType.Jumping
end

--[[
    Updates movement data based on input, character state, camera, and delta time.

    @param movementData Types.MovementData Current movement data
    @param inputData Types.InputData Current input snapshot
    @param character Model Player character model
    @param cameraCFrame CFrame Current camera orientation
    @param deltaTime number Time elapsed since last update
    @return Types.MovementData Updated movement data
]]
function MovementCore.UpdateMovement(
  movementData: Types.MovementData,
  inputData: Types.InputData,
  character: Model,
  cameraCFrame: CFrame,
  deltaTime: number
): Types.MovementData
  local currentTime: number = tick()

  movementData.IsGrounded = MovementCore.CheckGrounded(character)
  movementData.Stamina = staminaValue.Value
  movementData.IsExhausted = exhaustedValue.Value

  local newState: Types.MovementState =
    MovementCore.DetermineState(inputData, movementData, currentTime)

  if newState == "Jump" then
    local humanoid: Humanoid? = character:FindFirstChildOfClass("Humanoid")
    if humanoid then
      jumpRequest:FireServer()
      movementData.LastJumpTime = currentTime
    end
    newState = "Walk"
  end

  local targetVelocity: Vector3 =
    MovementCore.CalculateTargetVelocity(inputData.MoveVector, newState, cameraCFrame)
  movementData.Velocity =
    MovementCore.InterpolateVelocity(movementData.Velocity, targetVelocity, deltaTime)
  movementData.InputDirection = inputData.MoveVector
  movementData.State = newState

  return movementData
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return MovementCore
