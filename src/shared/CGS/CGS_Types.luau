--!strict

--[[
    - file: CGS_Types.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Core Game System (CGS) Centralized Types
]]

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local CGS_Types = {}

-- ============================================================================
-- REMOTES
-- ============================================================================

export type CGS_Remotes = {
  init: () -> (),
}

-- ============================================================================
-- MOVEMENT TYPES
-- ============================================================================

export type MovementState = "Idle" | "Walk" | "Sprint" | "Crouch" | "Prone" | "Jump"

export type JointName =
  "RootJoint"
  | "Neck"
  | "RightShoulder"
  | "LeftShoulder"
  | "RightHip"
  | "LeftHip"

export type KeyName = "W" | "A" | "S" | "D" | "LeftShift" | "LeftControl" | "C"

export type MovementDirection =
  "None"
  | "Forward"
  | "Backward"
  | "Left"
  | "Right"
  | "ForwardLeft"
  | "ForwardRight"
  | "BackwardLeft"
  | "BackwardRight"

export type JointData = {
  Joint: Motor6D,
  OriginalC0: CFrame,
  CurrentTilt: CFrame,
}

export type BodyAnimator = {
  Joints: { [JointName]: JointData },
  TweenInfo: TweenInfo,
  ActiveTweens: { [JointName]: RBXScriptConnection? },
}

export type MovementData = {
  State: MovementState,
  Velocity: Vector3,
  Stamina: number,
  LastJumpTime: number,
  InputDirection: Vector3,
  IsGrounded: boolean,
  IsExhausted: boolean,
}

export type InputData = {
  MoveVector: Vector3,
  Direction: MovementDirection,
  SprintHeld: boolean,
  CrouchHeld: boolean,
  ProneHeld: boolean,
  JumpPressed: boolean,
}

export type KeyPressedMap = {
  [KeyName]: boolean,
}

export type Connection = {
  Disconnect: () -> (),
}

export type Signal = {
  Connect: (self: Signal, callback: (...any) -> ()) -> Connection,
  Fire: (self: Signal, ...any) -> (),
  Destroy: (self: Signal) -> (),
}

export type Listener = {
  Connected: boolean,
  Callback: (...any) -> (),
}

export type MovementConfig = {
  -- Speed multipliers (studs/second)
  IdleSpeed: number,
  WalkSpeed: number,
  SprintSpeed: number,
  CrouchSpeed: number,
  ProneSpeed: number,

  -- Directional modifiers
  StrafeSpeedMultiplier: number, -- A/D movement cap (15%)
  BackwardSpeedMultiplier: number, -- S movement cap (50%)

  -- Physics
  Acceleration: number,
  Deceleration: number,

  -- Stamina system
  MaxStamina: number,
  StaminaRegenRate: number,
  SprintStaminaDrain: number,
  JumpStaminaCost: number,

  -- Jump mechanics
  JumpPower: number,
  JumpCooldown: number,

  -- State transition speeds
  TransitionSpeed: number,

  -- Gamepad
  Gamepad_Deadzone: number,

  -- Exhaustion
  DepletionWindow: number,
  MaxDepletions: number,
  ExhaustDuration: number,
  RegenMultiplierExhausted: number,
}

-- ============================================================================
-- OBSERVATION TYPES
-- ============================================================================

export type CameraEffect = {
  Enabled: boolean,
  Priority: number,
  Init: ((self: CameraEffect) -> ())?,
  Reset: ((self: CameraEffect) -> ())?,
  Update: (self: CameraEffect, deltaTime: number, camera: Camera) -> (),
  OnEnable: ((self: CameraEffect) -> ())?,
  OnDisable: ((self: CameraEffect) -> ())?,
  [string]: any, -- Allow additional fields like Turn, Character, etc.
}

export type BodyView = {
  Enabled: boolean,
  Priority: number,
}

export type BobConfig = {
  Enabled: boolean,
  Priority: number,
  MovementThreshold: number,
}

export type SwayConfig = {
  Enabled: boolean,
  Priority: number,
  HeadTurnAmount: number,
  TurnSpeed: number,
  TurnClamp: number,
  IdleAmount: Vector3,
  IdleSpeed: number,
  IdleThreshold: number,
}

export type FOVConfig = {
  Enabled: boolean,
  Priority: number,
  DefaultFOV: number,
  SprintFOV: number,
  JumpFOV: number,
  FreeFallFOV: number,
  SwimFOV: number,
  CrouchFOV: number,
  ProneFOV: number,
  TransitionSpeed: number,
}

export type DirectionalHeadConfig = {
  Enabled: boolean,
  Priority: number,
  ActivationDistance: number,
}

export type MotionBlurConfig = {
  Enabled: boolean,
  Priority: number,
  maxBlur: number,
  rotationSensitivity: number,
  speedThreshold: number,
  FadeSpeed: number,
}

export type UnderwaterEffectConfig = {
  Enabled: boolean,
  Priority: number,
}

export type ShakePreset = {
  intensity: number,
  frequency: number,
  duration: number,
  fadeIn: number,
  fadeOut: number,
  direction: Vector3,
  damping: number,
  roughness: number,
}

export type ShakeInstance = {
  preset: ShakePreset,
  timeRemaining: number,
  currentIntensity: number,
  seed: number,
  id: string,
}

export type ShakePresets = { [string]: ShakePreset }

export type PartialShakePreset = {
  intensity: number?,
  frequency: number?,
  duration: number?,
  fadeIn: number?,
  fadeOut: number?,
  direction: Vector3?,
  damping: number?,
  roughness: number?,
}

export type IgnoredAccessories = { [string]: boolean }

export type ObservationConfig = {
  BodyView: BodyView,
  Bobbing: BobConfig,
  Sway: SwayConfig,
  DynamicFOV: FOVConfig,
  MotionBlur: MotionBlurConfig,
  DirectionalHead: DirectionalHeadConfig,
  UnderwaterEffect: UnderwaterEffectConfig,
  ShakePresets: ShakePresets,
  IgnoredAccessories: IgnoredAccessories,
  IntensityMultiplier: number,
}

-- ============================================================================
-- ANTI-EXPLOIT TYPES
-- ============================================================================
export type ExploitFlags = {
  CanSpeed: boolean,
  CanSuperJump: boolean,
}

export type NoExploitsMovementConfig = {
  SpeedThreshold: number,
  JumpHeightMax: number,
}

export type PlayerFlagsMap = {
  [Player]: ExploitFlags,
}

export type ExploitFlagKey = "CanSpeed" | "CanSuperJump"

export type SpeedDetectionConfig = {
  CheckInterval: number,
  SampleSize: number,
  ToleranceMultiplier: number,
}

export type JumpDetectionConfig = {
  CheckInterval: number,
  MaxConsecutiveJumps: number,
  Cooldown: number,
}

export type ViolationConfig = {
  MaxViolations: number,
  Decay: number,
  Thresholds: {
    Warning: number,
    Kick: number,
    Ban: number,
  },
}

export type PerformanceConfig = {
  UpdateRate: number,
  MaxPlayersPerFrame: number,
}

export type DetectionConfig = {
  Speed: SpeedDetectionConfig,
  Jump: JumpDetectionConfig,
  Violation: ViolationConfig,
  Performance: PerformanceConfig,
  KillYThreshold: number,
  playerCheckIndex: number,
  maxChecksPerFrame: number,
}

export type SystemToggles = {
  EnableSpeedCheck: boolean,
  EnableJumpCheck: boolean,
  EnableViolationSystem: boolean,
  EnableLogging: boolean,
  EnableYKillCheck: boolean,
}

export type AntiExploitConfig = {
  SpecialUsers: { number },
  Movement: NoExploitsMovementConfig,
  Detection: DetectionConfig,
  Toggles: SystemToggles,
  IgnorePrivateServers: boolean,
}

-- ============================================================================
-- PLAYER TRACKING TYPES
-- ============================================================================
export type PlayerTrackingData = {
  positionHistory: { Vector3 },
  timeHistory: { number },
  historyIndex: number,
  lastGrounded: number,
  airTime: number,
  jumpCount: number,
  lastJumpTime: number,
  violations: number,
  lastViolationTime: number,
  noClipSamples: number,
  lastValidPosition: Vector3,
}

-- ============================================================================
-- MAIN CONFIGURATION TYPE
-- ============================================================================
export type Configuration = {
  General: {
    DebugMode: boolean,
    PerformanceMonitor: boolean,
  },
  Movement: MovementConfig,
  Observation: ObservationConfig,
  AntiExploit: AntiExploitConfig,
}

-- ============================================================================
-- EXPORTS
-- ============================================================================
return CGS_Types
