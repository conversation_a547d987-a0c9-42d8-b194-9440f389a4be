-- --!strict

--[[
    - file: TBRDS_REMOTES.LUAU

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Remote event management module for the TBRDS (Tag-Based Role Distribution System).
      - Provides a centralized, type-safe, and consistent API for managing RemoteEvents used by TBRDS.
      - Handles creation, caching, and retrieval of RemoteEvents for both server and client.
      - Ensures all remotes are initialized and available before use.
      - Supports debug logging and cleanup for testing.
]]

-- =============================================================================
-- SERVICES
-- =============================================================================

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- =============================================================================
-- MODULES
-- =============================================================================

local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)

-- =============================================================================
-- MODULE DEFINITION
-- =============================================================================
local TBRDSRemotes = {}

-- =============================================================================
-- TYPE ANNOTATIONS & DEFINITIONS
-- =============================================================================
--[[
    RemoteEventMap: Dictionary of remote event names to RemoteEvent instances.
    TBRDSRemotes: Public API type for this module.
]]
export type RemoteEventMap = { [string]: RemoteEvent }
export type TBRDSRemotes = typeof(TBRDSRemotes) & {
  GetRemotesFolder: () -> Folder?,
  GetTagUpdateRemote: () -> RemoteEvent?,
  GetTagRequestRemote: () -> RemoteEvent?,
  GetTagValidationRemote: () -> RemoteEvent?,
  GetSecurityReportRemote: () -> RemoteEvent?,
  InitializeRemotes: () -> boolean,
  WaitForRemotes: () -> boolean,
  CleanupRemotes: () -> (),
  GetAllRemotes: () -> RemoteEventMap,
  AreRemotesReady: () -> boolean,
}

-- =============================================================================
-- VARIABLES
-- =============================================================================
-- Remote event cache for fast lookup and consistency.
local remoteEvents: RemoteEventMap = {}
local remotesFolder: Folder? = nil

-- =============================================================================
-- CONSTANTS
-- =============================================================================
local REMOTES_FOLDER_NAME = "Remotes"
local TBRDS_FOLDER_NAME = "TBRDS"

-- =============================================================================
-- DEBUG LOGGING
-- =============================================================================
--[[
    Print debug messages if DebugMode is enabled in config.
    @param message: string -- Message to print.
]]
local function debugLog(message: string): ()
  if TBRDSConfig.Settings.DebugMode then
    print(string.format("[TBRDS:Remotes]: %s", message))
  end
end

-- =============================================================================
-- INTERNAL HELPERS
-- =============================================================================
--[[
    Get or create the TBRDS folder in ReplicatedStorage.
    @return Folder -- The TBRDS folder.
    @throws error if folder is not found on client after timeout.
]]
local function getTBRDSFolder(): Folder
  local tbrdsFolder = ReplicatedStorage:FindFirstChild(TBRDS_FOLDER_NAME)

  if not tbrdsFolder then
    if RunService:IsServer() then
      tbrdsFolder = Instance.new("Folder")
      tbrdsFolder.Name = TBRDS_FOLDER_NAME
      tbrdsFolder.Parent = ReplicatedStorage
      debugLog("Created TBRDS folder")
    else
      -- Client waits for server to create it
      tbrdsFolder = ReplicatedStorage:WaitForChild(TBRDS_FOLDER_NAME, 10)
      if not tbrdsFolder then
        error("TBRDS: Failed to find TBRDS folder after timeout")
      end
    end
  end

  return tbrdsFolder
end

-- =============================================================================
-- PUBLIC API
-- =============================================================================

--[[
    Get or create the remotes folder under TBRDS in ReplicatedStorage.
    @return Folder? -- The remotes folder, or nil if unavailable.
]]
function TBRDSRemotes.GetRemotesFolder(): Folder?
  if remotesFolder and remotesFolder.Parent then
    return remotesFolder
  end

  local tbrdsFolder = getTBRDSFolder()
  if not tbrdsFolder then
    warn("TBRDS: Cannot access TBRDS folder")
    return nil
  end

  remotesFolder = tbrdsFolder:FindFirstChild(REMOTES_FOLDER_NAME) :: Folder?

  if not remotesFolder then
    if RunService:IsServer() then
      remotesFolder = Instance.new("Folder") -- Create new folder
      remotesFolder.Name = REMOTES_FOLDER_NAME
      remotesFolder.Parent = tbrdsFolder
      debugLog("Created remotes folder")
    else
      remotesFolder = tbrdsFolder:WaitForChild(REMOTES_FOLDER_NAME, 10) :: Folder
      if not remotesFolder then
        warn("TBRDS: Failed to find remotes folder after timeout")
        return nil
      end
    end
  end
  return remotesFolder
end

--[[
    Get or create a RemoteEvent by name, using cache for efficiency.
    @param eventName: string -- Name of the remote event.
    @return RemoteEvent? -- The RemoteEvent instance, or nil if unavailable.
]]
local function getRemoteEvent(eventName: string): RemoteEvent?
  -- Check cache first
  if remoteEvents[eventName] and remoteEvents[eventName].Parent then
    return remoteEvents[eventName]
  end

  local folder = TBRDSRemotes.GetRemotesFolder()
  if not folder then
    warn("TBRDS: Cannot access remotes folder")
    return nil
  end

  -- Look for existing event
  local remoteEvent = folder:FindFirstChild(eventName) :: RemoteEvent?

  if not remoteEvent then
    if RunService:IsServer() then
      remoteEvent = Instance.new("RemoteEvent")

      -- Ignore type errors here as we are creating a new RemoteEvent
      remoteEvent.Name = eventName
      remoteEvent.Parent = folder
      debugLog(string.format("Created remote event: %s", eventName))
    else
      -- Client waits for server to create it
      remoteEvent = folder:WaitForChild(eventName, 10) :: RemoteEvent?
      if not remoteEvent then
        warn(string.format("TBRDS: Failed to find remote event '%s' after timeout", eventName))
        return nil
      end
    end
  end

  -- Cache the remote event if it exists
  if remoteEvent then
    remoteEvents[eventName] = remoteEvent
  end
  return remoteEvent
end

--[[
    Get the RemoteEvent for tag updates.
    @return RemoteEvent? -- Tag update RemoteEvent.
]]
function TBRDSRemotes.GetTagUpdateRemote(): RemoteEvent?
  return getRemoteEvent(TBRDSConfig.Remotes.TagUpdate)
end

--[[
    Get the RemoteEvent for tag requests.
    @return RemoteEvent? -- Tag request RemoteEvent.
]]
function TBRDSRemotes.GetTagRequestRemote(): RemoteEvent?
  return getRemoteEvent(TBRDSConfig.Remotes.TagRequest)
end

--[[
    Get the RemoteEvent for tag validation.
    @return RemoteEvent? -- Tag validation RemoteEvent.
]]
function TBRDSRemotes.GetTagValidationRemote(): RemoteEvent?
  return getRemoteEvent(TBRDSConfig.Remotes.TagValidation)
end

--[[
    Get the RemoteEvent for security reports.
    @return RemoteEvent? -- Security report RemoteEvent.
]]
function TBRDSRemotes.GetSecurityReportRemote(): RemoteEvent?
  return getRemoteEvent(TBRDSConfig.Remotes.SecurityReport)
end

--[[
    Initialize all configured RemoteEvents (server only).
    Creates all remotes defined in TBRDSConfig.Remotes.
    @return boolean -- True if all remotes initialized, false otherwise.
]]
function TBRDSRemotes.InitializeRemotes(): boolean
  if not RunService:IsServer() then
    warn("TBRDS: InitializeRemotes should only be called on the server")
    return false
  end

  debugLog("Initializing remote events...")

  local success = true

  -- Create all configured remote events
  for _, remoteName in pairs(TBRDSConfig.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if not remoteEvent then
      warn(string.format("TBRDS: Failed to create remote event: %s", remoteName))
      success = false
    end
  end

  if success then
    debugLog("All remote events initialized successfully")
  else
    warn("TBRDS: Some remote events failed to initialize")
  end

  return success
end

--[[
    Wait for all configured RemoteEvents to be available (client only).
    @return boolean -- True if all remotes found, false otherwise.
]]
function TBRDSRemotes.WaitForRemotes(): boolean
  if RunService:IsServer() then
    warn("TBRDS: WaitForRemotes should only be called on the client")
    return false
  end

  debugLog("Waiting for remote events...")

  local success = true

  -- Wait for all configured remote events
  for _, remoteName in pairs(TBRDSConfig.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if not remoteEvent then
      warn(string.format("TBRDS: Failed to find remote event: %s", remoteName))
      success = false
    end
  end

  if success then
    debugLog("All remote events found successfully")
  else
    warn("TBRDS: Some remote events were not found")
  end

  return success
end

--[[
    Clean up all RemoteEvents and folders (server only, for testing/cleanup).
    Destroys the remotes folder and clears cache.
]]
function TBRDSRemotes.CleanupRemotes(): ()
  if not RunService:IsServer() then
    warn("TBRDS: CleanupRemotes should only be called on the server")
    return
  end

  debugLog("Cleaning up remote events...")

  -- Clear cache
  remoteEvents = {}

  -- Remove remotes folder if it exists
  if remotesFolder and remotesFolder.Parent then
    remotesFolder:Destroy()
    remotesFolder = nil
  end

  debugLog("Remote events cleaned up")
end

--[[
    Get a dictionary of all configured RemoteEvents (for debugging).
    @return RemoteEventMap -- Map of config keys to RemoteEvent instances.
]]
function TBRDSRemotes.GetAllRemotes(): RemoteEventMap
  local allRemotes: RemoteEventMap = {}

  for configKey, remoteName in pairs(TBRDSConfig.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if remoteEvent then
      allRemotes[configKey] = remoteEvent
    end
  end

  return allRemotes
end

--[[
    Check if all configured RemoteEvents are available.
    @return boolean -- True if all remotes are ready, false otherwise.
]]
function TBRDSRemotes.AreRemotesReady(): boolean
  for _, remoteName in pairs(TBRDSConfig.Remotes) do
    local remoteEvent = getRemoteEvent(remoteName)
    if not remoteEvent then
      return false
    end
  end

  return true
end

-- =============================================================================
-- INITIALIZATION
-- =============================================================================
--[[
    On server: initializes all remotes immediately.
    On client: waits for all remotes to be available asynchronously.
]]
if RunService:IsServer() then
  -- Server initializes remotes immediately
  TBRDSRemotes.InitializeRemotes()
else
  -- Client waits for remotes to be available
  task.spawn(function()
    TBRDSRemotes.WaitForRemotes()
  end)
end

-- =============================================================================
-- EXPORTS
-- =============================================================================
return TBRDSRemotes :: TBRDSRemotes
