--!strict

--[[
    - file: TBRDS_ROLEHANDLER.LUAU

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Role Handlers Module for Tag-Based Role Display System (TBRDS).
      - Defines role logic, check functions, and visual styles for all tags.
      - Provides a single API to determine a player's highest-priority role.
]]

-- =============================================================================
-- TYPES
-- =============================================================================
--[[
    RoleHandler: Structure for a role's logic and style.
    @field Check (player: Player) -> boolean -- Returns true if player qualifies for this role
    @field Style table -- Visual style for the tag
        @field Color Color3 -- Text color
        @field Font Enum.Font -- Font for the tag
        @field Image string? -- Optional image asset
        @field Gradient { Colors: {Color3}, Rotation: number }? -- Optional gradient
        @field FontStyle string? -- Optional font style
        @field TextStroke { Color: Color3, Transparency: number }? -- Optional text stroke
        @field GetText (player: Player) -> string -- Optional custom tag text
]]

--[[
    RoleHandlersModule: API for role handlers.
    @field GetPlayerRole (player: Player) -> string -- Returns the highest role name for a player
    @field <RoleName> RoleHandler -- Each role is a RoleHandler
]]

-- =============================================================================
-- ROLE HANDLERS TABLE
-- =============================================================================
local RoleHandlers = {}

local MarketplaceService = game:GetService("MarketplaceService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Types = require(ReplicatedStorage.TBRDS.TBRDS_Types)
local TBRDSConstants = Types.TBRDSConstants

-- |----------------| Default user role |----------------|
--[[
    User: Default role for all players.
    Always returns true for Check.
]]
RoleHandlers.User = {
  Check = function(_)
    return true -- Always return true as this is the default role
  end,
  Style = {
    Color = Color3.fromRGB(255, 255, 255), -- White
    Font = Enum.Font.SourceSans,
    GetText = function(player)
      local teamName = (typeof(player.Team) == "Instance" and player.Team.Name) or "No Team"
      return string.format("[%s] - %s", teamName, player.Name)
    end,
  },
}

-- |----------------| Supporter role |----------------|
--[[
    Supporter: For players who own the donation gamepass.
    Checks gamepass ownership and group rank.
]]
RoleHandlers.Supporter = {
  Check = function(player)
    local hasPass = false
    local success, result = pcall(function()
      return MarketplaceService:UserOwnsGamePassAsync(
        player.UserId,
        TBRDSConstants.GAMEPASS_IDS.Supporter
      )
    end)
    if success then
      hasPass = result
    end
    return hasPass or player:GetRankInGroup(TBRDSConstants.GROUP_ID) == 2
  end,
  Style = {
    Color = Color3.fromRGB(128, 0, 128), -- Purple
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=75475742951834",
  },
}

-- |----------------| Trusted user role |----------------|
--[[
    Trusted: For players with Trusted rank in group.
]]
RoleHandlers.Trusted = {
  Check = function(player)
    return player:GetRankInGroup(TBRDSConstants.GROUP_ID) == 248
  end,
  Style = {
    Color = Color3.fromRGB(14, 149, 1), -- Light Blue
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=124860305809176",
    TextStroke = {
      Color = Color3.fromRGB(100, 100, 200),
      Transparency = 0.7,
    },
  },
}

-- |----------------| Developer role |----------------|
--[[
    Developer: For players with Developer rank in group.
]]
RoleHandlers.Developer = {
  Check = function(player)
    return player:GetRankInGroup(TBRDSConstants.GROUP_ID) == 249
  end,
  Style = {
    Color = Color3.fromRGB(255, 17, 1), -- Red
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=121618039480779",
  },
}

-- |----------------| Junior Moderator role |----------------|
--[[
    Junior Moderator: For players with Junior Moderator rank in group.
]]
RoleHandlers["Junior Moderator"] = {
  Check = function(player)
    return player:GetRankInGroup(TBRDSConstants.GROUP_ID) == 250
      and player:GetRankInGroup(TBRDSConstants.GROUP_ID) < 251
  end,
  Style = {
    Color = Color3.fromRGB(99, 225, 181), -- Cyan-green
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=127518605052393",
    FontStyle = "Bold",
  },
}

-- |----------------| Investors role |----------------|
--[[
    Investors: For players with Investors rank in group.
]]
RoleHandlers.Investors = {
  Check = function(player)
    return player:GetRankInGroup(TBRDSConstants.GROUP_ID) == 251
  end,
  Style = {
    Color = Color3.fromRGB(255, 215, 0), -- Gold
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=118942866906979",
    Gradient = {
      Colors = { Color3.fromRGB(255, 215, 0), Color3.fromRGB(255, 165, 0) },
      Rotation = 90,
    },
  },
}

-- |----------------| Game Moderator role |----------------|
--[[
    Game Moderator: For players with Game Moderator rank in group.
]]
RoleHandlers["Game Moderator"] = {
  Check = function(player)
    return player:GetRankInGroup(TBRDSConstants.GROUP_ID) == 252
  end,
  Style = {
    Color = Color3.fromRGB(0, 0, 255), -- Blue
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=82778826477473",
    TextStroke = {
      Color = Color3.fromRGB(0, 0, 100),
      Transparency = 0.6,
    },
  },
}

-- |----------------| Senior Moderator role |----------------|
--[[
    Senior Moderator: For players with Senior Moderator rank in group.
]]
RoleHandlers["Senior Moderator"] = {
  Check = function(player)
    return player:GetRankInGroup(TBRDSConstants.GROUP_ID) == 253
  end,
  Style = {
    Color = Color3.fromRGB(0, 0, 139), -- Dark Blue
    Font = Enum.Font.SourceSansBold,
    Image = "http://www.roblox.com/asset/?id=115347218194547",
    Gradient = {
      Colors = { Color3.fromRGB(0, 0, 139), Color3.fromRGB(25, 25, 180) },
      Rotation = 135,
    },
    TextStroke = {
      Color = Color3.fromRGB(0, 0, 70),
      Transparency = 0.5,
    },
  },
}

-- |----------------| Co-Founder role |----------------|
--[[
    Anonmancer: Co-Founder role.
]]
RoleHandlers.Anonmancer = {
  Check = function(player)
    return player:GetRankInGroup(TBRDSConstants.GROUP_ID) == 254
  end,
  Style = {
    Color = Color3.fromRGB(3, 164, 3), -- Emeral Green that beautiful damion likes
    Font = Enum.Font.Kalam,
    Image = "http://www.roblox.com/asset/?id=130206582408033",
    TextStroke = {
      Color = Color3.fromRGB(0, 100, 100),
      Transparency = 0.3,
    },
  },
}

-- |----------------| Founder role |----------------|
--[[
    BleckWolf25: Founder role.
]]
RoleHandlers.BleckWolf25 = {
  Check = function(player)
    return player:GetRankInGroup(TBRDSConstants.GROUP_ID) == 255
  end,
  Style = {
    Color = Color3.fromRGB(0, 255, 255), -- Cyan
    Font = Enum.Font.Kalam,
    Image = "http://www.roblox.com/asset/?id=84748237738215",
    Gradient = {
      Colors = { Color3.fromRGB(0, 255, 255), Color3.fromRGB(255, 0, 255) },
      Rotation = 180,
    },
    TextStroke = {
      Color = Color3.fromRGB(0, 100, 100),
      Transparency = 0.3,
    },
  },
}

-- =============================================================================
-- PUBLIC API
-- =============================================================================

--[[
    GetPlayerRole: Returns the highest-priority role name for a player.
    Checks roles in priority order (highest to lowest).
    @param player Player -- The player to check.
    @return string -- The name of the highest role, or "User" if none match.
]]
function RoleHandlers.GetPlayerRole(player)
  for _, roleName in ipairs(TBRDSConstants.ROLE_PRIORITY) do
    local roleHandler = RoleHandlers[roleName]
    if
      type(roleHandler) == "table"
      and type(roleHandler.Check) == "function"
      and roleHandler.Check(player)
    then
      return roleName
    end
  end
  return "User"
end

-- =============================================================================
-- EXPORTS
-- =============================================================================
return RoleHandlers
