--!strict

--[[
    - file: TBRDS_API

	- version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Centralized, type-safe API for the Tag-Based Role Display System (TBRDS).
      - Provides methods for role management, player tagging, validation, event handling,
      and performance monitoring. Used by both client and server components.
      - All types and constants are referenced from TBRDS_Types for maintainability.
      - Player role management (get/set/validate)
      - Player tag management (get/refresh)
      - Event subscription for tag changes
      - Performance and system health monitoring
      - Cache operations (use TagService directly)
]]

-- =============================================================================
-- SERVICES
-- =============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- =============================================================================
-- MODULES
-- =============================================================================

-- Module dependencies
local EventSystem = require(script.Parent.TBRDS_EventSystem)
local PerformanceMonitor = require(script.Parent.TBRDS_PerformanceMonitor)
local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Utils = require(script.Parent.TBRDS_Utils)

-- =============================================================================
-- TYPE ANNOTATIONS & DEFINITIONS
-- =============================================================================

-- Main type import
local Types = require(script.Parent.Parent.TBRDS_Types)

-- Type imports
type RoleName = Types.RoleName
type TBRDSApi = Types.TBRDSApi
type ValidationResult = Types.ValidationResult
type PlayerTagData = Types.PlayerTagData
type PerformanceMetrics = Types.PerformanceMetrics
type RoleStyle = Types.RoleStyle
type EventCallback = Types.EventCallback
type PlayerCache = Types.PlayerCache
local TBRDSConstants = Types.TBRDSConstants

-- =============================================================================
-- MODULE TABLE
-- =============================================================================

local TBRDSAPI = {}

-- =============================================================================
-- INTERNAL STATE
-- =============================================================================

-- Service access (set by the service manager)
local serviceManager: Types.ServiceManager? = nil

-- =============================================================================
-- DEBUG LOGGING
-- =============================================================================

-- Debug logging (controlled by TBRDSConfig.Settings.DebugMode)
local function debugLog(message: string)
  if TBRDSConfig.Settings.DebugMode then
    print(string.format("[TBRDS:API]: %s", message))
  end
end

-- =============================================================================
-- INITIALIZATION
-- =============================================================================
--[[
    Initializes the API with a service manager instance.
    Should be called once on server startup.
    @param serviceManagerInstance: The service manager to use for dependency resolution.
]]
function TBRDSAPI.Initialize(serviceManagerInstance: Types.ServiceManager): ()
  serviceManager = serviceManagerInstance
  debugLog("API initialized with service manager")
end

-- =============================================================================
-- API FUNCTIONS
-- =============================================================================

--[[
    Safely retrieves a service from the service manager.
    @param serviceName: Name of the service to retrieve.
    @return: Service instance or nil if unavailable.
]]
local function getService(serviceName: string): Types.TagService | Types.RoleService?
  if not serviceManager then
    warn("TBRDS API: Service manager not initialized")
    return nil
  end
  local service = serviceManager.GetService(serviceName)
  if not service then
    warn(`TBRDS API: Service '{serviceName}' not found`)
    return nil
  end
  return service
end
--[[
    Gets the current role of a player.
    @param player: Player to check.
    @return: RoleName (default if not found/invalid)
]]
function TBRDSAPI.GetPlayerRole(player: Player): RoleName
  local validation = Utils.validatePlayer(player)
  if not validation.Success then
    return TBRDSConstants.ROLE_PRIORITY[#TBRDSConstants.ROLE_PRIORITY] -- Default role
  end
  local tagService = getService("Tag") :: Types.TagService?
  if tagService then
    local tagData = tagService.GetPlayerTagData(player)
    if tagData then
      return tagData.Role
    end
  end
  local roleService = getService("Role") :: Types.RoleService?
  if roleService then
    return roleService.GetPlayerRole(player)
  end
  return TBRDSConstants.ROLE_PRIORITY[#TBRDSConstants.ROLE_PRIORITY]
end

--[[
    Sets a player's role (server-side only, validated).
    @param player: Player to set role for.
    @param roleName: Role to assign.
    @return: ValidationResult
]]
function TBRDSAPI.SetPlayerRole(player: Player, roleName: RoleName): ValidationResult
  local result: ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }
  if not RunService:IsServer() then
    result.ErrorCode = TBRDSConstants.ERROR_CODES.SECURITY_VIOLATION
    result.ErrorMessage = "SetPlayerRole can only be called on the server"
    return result
  end
  local playerValidation = Utils.validatePlayer(player)
  if not playerValidation.Success then
    return playerValidation
  end
  local roleService = getService("Role") :: Types.RoleService?
  if not roleService then
    result.ErrorCode = TBRDSConstants.ERROR_CODES.VALIDATION_FAILED
    result.ErrorMessage = "Role service not available"
    return result
  end
  local roleValidation = roleService.ValidatePlayerRole(player, roleName)
  if not roleValidation.Success then
    return roleValidation
  end
  local tagService = getService("Tag") :: Types.TagService?
  if not tagService then
    result.ErrorCode = TBRDSConstants.ERROR_CODES.VALIDATION_FAILED
    result.ErrorMessage = "Tag service not available"
    return result
  end
  return tagService.RefreshPlayerTag(player)
end

--[[
    Refreshes a player's tag (re-validates and updates tag data).
    @param player: Player to refresh tag for.
    @return: ValidationResult
]]
function TBRDSAPI.RefreshPlayerTag(player: Player): ValidationResult
  local result: ValidationResult = {
    Success = false,
    Role = nil,
    ErrorCode = nil,
    ErrorMessage = nil,
    SecurityFlags = nil,
  }
  local playerValidation = Utils.validatePlayer(player)
  if not playerValidation.Success then
    return playerValidation
  end
  local tagService = getService("Tag") :: Types.TagService?
  if not tagService then
    result.ErrorCode = TBRDSConstants.ERROR_CODES.VALIDATION_FAILED
    result.ErrorMessage = "Tag service not available"
    return result
  end
  return tagService.RefreshPlayerTag(player)
end

--[[
    Gets tag data for a player, or nil if invalid.
    @param player: Player to get tag data for.
    @return: PlayerTagData or nil
]]
function TBRDSAPI.GetPlayerTagData(player: Player): PlayerTagData?
  local validation = Utils.validatePlayer(player)

  if not validation.Success then
    return nil
  end

  local tagService = getService("Tag") :: Types.TagService?

  if tagService then
    return tagService.GetPlayerTagData(player)
  end

  return nil
end

--[[
    Checks if a role is valid for a player.
    @param player: Player to check.
    @param roleName: Role to validate.
    @return: boolean
]]
function TBRDSAPI.IsRoleValid(player: Player, roleName: RoleName): boolean
  local validation = Utils.validatePlayer(player)

  if not validation.Success then
    return false
  end

  local roleService = getService("Role") :: Types.RoleService

  if roleService then
    local roleValidation = roleService.ValidatePlayerRole(player, roleName)
    return roleValidation.Success
  end
  return false
end

--[[
    Gets style information for a given role.
    @param roleName: Role to get style for.
    @return: RoleStyle or nil
]]
function TBRDSAPI.GetRoleStyle(roleName: RoleName): RoleStyle?
  local roleService = getService("Role") :: Types.RoleService

  if roleService then
    return roleService.GetRoleStyle(roleName)
  end

  return nil
end

--[[
    Subscribes to tag change events.
    @param callback: Function to call on tag change.
    @return: Subscription ID string
]]
function TBRDSAPI.SubscribeToTagChanges(callback: EventCallback): string
  return EventSystem.Subscribe(TBRDSConstants.EVENT_NAMES.TagChanged, callback)
end

--[[
    Unsubscribes from tag change events.
    @param subscriptionId: ID returned by SubscribeToTagChanges.
    @return: boolean (success)
]]
function TBRDSAPI.UnsubscribeFromTagChanges(subscriptionId: string): boolean
  return EventSystem.Unsubscribe(subscriptionId)
end

--[[
    Gets current performance metrics.
    @return: PerformanceMetrics
]]
function TBRDSAPI.GetPerformanceMetrics(): PerformanceMetrics
  return PerformanceMonitor.GetDetailedMetrics()
end

--[[
    Sets debug mode (updates config and logs).
    @param enabled: Whether debug mode is enabled.
]]
function TBRDSAPI.SetDebugMode(enabled: boolean): ()
  -- In a real implementation, this would update TBRDSConfig.Settings.DebugMode
  debugLog(string.format("Debug mode %s", enabled and "enabled" or "disabled"))
end

--[[
    Gets all players with a specific role.
    @param roleName: Role to filter by.
    @return: Array of Player
]]
function TBRDSAPI.GetPlayersWithRole(roleName: RoleName): { Player }
  local roleService = getService("Role") :: Types.RoleService

  if roleService then
    return roleService.GetPlayersWithRole(roleName)
  end

  return {}
end

--[[
    Gets role distribution statistics (role name -> count).
    @return: Table mapping role names to player counts.
]]
function TBRDSAPI.GetRoleStatistics(): { [string]: number }
  local tagService = getService("Tag") :: Types.TagService

  if tagService then
    local stats = tagService.GetTagStatistics()
    return stats.roleDistribution or {}
  end

  return {}
end

--[[
    Gets system health and status summary.
    @return: Table with health metrics and status flags.
]]
function TBRDSAPI.GetSystemHealth(): { [string]: any }
  local health = {
    playersTracked = 0,
    cacheSize = 0,
    performanceMetrics = PerformanceMonitor.GetMetrics(),
    eventSystemMetrics = EventSystem.GetMetrics(),
    configurationValid = true,
    servicesHealthy = false,
  }
  if serviceManager then
    health.servicesHealthy = serviceManager.AreAllServicesHealthy()
    local serviceStatus = serviceManager.GetServiceStatusSummary()
    local tagServiceStatus = serviceStatus.services.Tag :: Types.TagServiceStatus?
    if tagServiceStatus and tagServiceStatus.details then
      health.playersTracked = tagServiceStatus.details.totalPlayers or 0
    else
      health.playersTracked = 0
    end
    health.cacheSize = health.playersTracked
  end
  return health
end

--[[
    Updates the player cache with new tag data.
    @param _player: Player to update.
    @param _tagData: New tag data to assign.
]]
function TBRDSAPI._UpdatePlayerCache(_player: Player, _tagData: PlayerTagData): ()
  -- Directly assign the tag using TagService
  local tagService = getService("Tag") :: Types.TagService

  -- If the service has a method for assigning tags, call it
  if tagService then
    tagService.RefreshPlayerTag(_player)
  end
end

--[[
    Removes a player from the cache (e.g., when they leave).
    @param _player: Player to remove.
]]
function TBRDSAPI._RemovePlayerFromCache(_player: Player): ()
  -- Remove the player from TagService's cache
  local tagService = getService("Tag") :: Types.TagService

  -- If the service has a method for handling player leaving, call it
  if tagService and tagService.HandlePlayerLeaving then
    tagService.HandlePlayerLeaving(_player)
  end
end

--[[
    Gets the player cache).
    @return: PlayerCache (table mapping Player to PlayerTagData)
]]
function TBRDSAPI._GetPlayerCache(): PlayerCache
  -- Directly access the TagService to get all player tags
  local tagService = getService("Tag") :: Types.TagService

  -- If the service has a method for getting all player tags, call it
  if tagService then
    return tagService.GetAllPlayerTags()
  end

  return {}
end

-- =============================================================================
-- DEBUG LOGGING
-- =============================================================================

--[[
    Module loaded log (for debug mode)
]]
debugLog("API module loaded")

-- =============================================================================
-- EXPORTS
-- =============================================================================
return TBRDSAPI
