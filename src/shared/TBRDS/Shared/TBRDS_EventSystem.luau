--!strict

--[[
    - file: TBRDS_EventSystem.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Implements the event system for the Tag-Based Role Display System (TBRDS)
      - Provides a type-safe, performance-oriented interface for event management
      - Supports subscription, firing, metrics, and history tracking
      - Designed for scalability and maintainability
]]

-- ============================================================================
-- SERVICES
-- ============================================================================

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================

local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Types = require(script.Parent.Parent.TBRDS_Types)

-- ============================================================================
-- TYPE ANNOTATIONS & DEFINITIONS
-- ============================================================================

-- Re-export types for better intellisense
export type TagEventType = Types.TagEventType -- This is not a error
export type TagEventData = Types.TagEventData -- This is not a error
export type EventCallback = Types.EventCallback -- This is not a error

-- Internal types for better type safety
type EventSubscribers = { [TagEventType]: { [string]: EventCallback } }
type EventMetrics = {
  eventsProcessed: number,
  subscribersNotified: number,
  errorCount: number,
  lastReset: number,
}

-- ============================================================================
-- CONSTANTS
-- ============================================================================

local TBRDSConstants = Types.TBRDSConstants

-- Configuration constants
local MAX_HISTORY_SIZE = 100
local CLEANUP_INTERVAL = 300 -- 5 minutes in seconds
local HISTORY_RETENTION_TIME = 3600 -- 1 hour in seconds

-- ============================================================================
-- MODULE DEFINITION
-- ============================================================================

local EventSystem = {}

-- ============================================================================
-- STATE VARIABLES
-- ============================================================================

-- Event subscribers storage - maps event types to subscription callbacks
local eventSubscribers: EventSubscribers = {}

-- Event history for debugging and analytics
local eventHistory: { TagEventData } = {}

-- Unique subscription counter for generating IDs
local subscriptionCounter = 0

-- Performance and usage metrics
local eventMetrics: EventMetrics = {
  eventsProcessed = 0,
  subscribersNotified = 0,
  errorCount = 0,
  lastReset = os.time(),
}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Initialize event subscriber tables for all defined event types
local eventTypes: { TagEventType } = {
  TBRDSConstants.EVENT_NAMES.TagChanged,
  TBRDSConstants.EVENT_NAMES.TagAssigned,
  TBRDSConstants.EVENT_NAMES.TagRemoved,
  TBRDSConstants.EVENT_NAMES.RoleValidated,
  TBRDSConstants.EVENT_NAMES.SecurityViolation,
  TBRDSConstants.EVENT_NAMES.ConfigurationChanged,
}

-- For each event name, subscribe it
for _, eventName in ipairs(eventTypes) do
  eventSubscribers[eventName] = {}
end

-- ============================================================================
-- DEBUG LOGGING
-- ============================================================================

--[[
	@private
	@function debugLog
	@description Conditional debug logging based on configuration settings
	@param message The debug message to log
]]
local function debugLog(message: string): ()
  if TBRDSConfig.Settings.DebugMode and TBRDSConfig.Debug.LogValidationEvents then
    print(`[TBRDS:EventSystem]: {message}`)
  end
end

-- ============================================================================
-- EVENT SYSTEM FUNCTIONS
-- ============================================================================

--[[
	@private
	@function generateSubscriptionId
	@description Generates a unique subscription identifier
	@return A unique string identifier for the subscription
]]
local function generateSubscriptionId(): string
  subscriptionCounter += 1
  return `sub_{subscriptionCounter}_{os.time()}`
end

--[[
	@private
	@function validateEventData
	@description Validates event data structure and required fields
	@param eventType The type of event being validated
	@param eventData The event data to validate
	@return Boolean indicating if the event data is valid
]]
local function validateEventData(eventType: TagEventType, eventData: TagEventData): boolean
  -- Validate player field
  if not eventData.Player or not eventData.Player:IsA("Player") then
    warn(`TBRDS EventSystem: Invalid Player in {eventType} event`)
    return false
  end

  -- Validate new role field
  if not eventData.NewRole or type(eventData.NewRole) ~= "string" or eventData.NewRole == "" then
    warn(`TBRDS EventSystem: Invalid NewRole in {eventType} event`)
    return false
  end

  -- Validate timestamp field
  if
    not eventData.Timestamp
    or type(eventData.Timestamp) ~= "number"
    or eventData.Timestamp <= 0
  then
    warn(`TBRDS EventSystem: Invalid Timestamp in {eventType} event`)
    return false
  end

  -- Validate source field
  if not eventData.Source or type(eventData.Source) ~= "string" or eventData.Source == "" then
    warn(`TBRDS EventSystem: Invalid Source in {eventType} event`)
    return false
  end

  return true
end

--[[
	@private
	@function addToHistory
	@description Adds an event to history with automatic size management
	@param eventData The event data to add to history
]]
local function addToHistory(eventData: TagEventData): ()
  table.insert(eventHistory, eventData)

  -- Maintain history size limit for memory efficiency
  if #eventHistory > MAX_HISTORY_SIZE then
    table.remove(eventHistory, 1)
  end
end

--[[
	@private
	@function safelyInvokeCallback
	@description Safely invokes a callback with error handling
	@param callback The callback function to invoke
	@param eventData The event data to pass to the callback
	@param subscriptionId The subscription ID for error reporting
	@return Boolean indicating if the callback was successfully invoked
]]
local function safelyInvokeCallback(
  callback: EventCallback,
  eventData: TagEventData,
  subscriptionId: string
): boolean
  local success, errorMessage = pcall(callback, eventData)

  if not success then
    warn(`TBRDS EventSystem: Error in callback '{subscriptionId}': {errorMessage}`)
    eventMetrics.errorCount += 1
    return false
  end

  return true
end

-- ============================================================================
-- PUBLIC API
-- ============================================================================

--[[
	@method Subscribe
	@description Subscribe to a specific event type with a callback function

	The callback will be invoked whenever the specified event type is fired.
	Returns a unique subscription ID that can be used to unsubscribe later.

	@param eventType The event type to subscribe to (must be from TBRDSConstants.EVENT_NAMES)
	@param callback The function to call when the event is fired
	@return Unique subscription ID string, or empty string if subscription failed

	@example
	```lua
	local subscriptionId = EventSystem.Subscribe("ROLE_ASSIGNED", function(eventData)
		print(`{eventData.Player.Name} got role: {eventData.NewRole}`)
	end)
	```
]]
function EventSystem.Subscribe(eventType: TagEventType, callback: EventCallback): string
  -- Validate event type exists
  if not eventSubscribers[eventType] then
    warn(`TBRDS EventSystem: Unknown event type '{eventType}'`)
    return ""
  end

  -- Validate callback is a function
  if type(callback) ~= "function" then
    warn("TBRDS EventSystem: Callback must be a function")
    return ""
  end

  local subscriptionId = generateSubscriptionId()
  eventSubscribers[eventType][subscriptionId] = callback

  debugLog(`Subscribed to '{eventType}' with ID '{subscriptionId}'`)
  return subscriptionId
end

--[[
	@method Unsubscribe
	@description Remove a subscription using its unique ID

	@param subscriptionId The subscription ID returned by Subscribe()
	@return Boolean indicating if the unsubscription was successful

	@example
	```lua
	local wasUnsubscribed = EventSystem.Unsubscribe(subscriptionId)
	if not wasUnsubscribed then
		warn("Failed to unsubscribe - subscription not found")
	end
	```
]]
function EventSystem.Unsubscribe(subscriptionId: string): boolean
  -- Search through all event types for the subscription
  for eventType, subscribers in pairs(eventSubscribers) do
    if subscribers[subscriptionId] then
      subscribers[subscriptionId] = nil
      debugLog(`Unsubscribed '{subscriptionId}' from '{eventType}'`)
      return true
    end
  end

  warn(`TBRDS EventSystem: Subscription ID '{subscriptionId}' not found`)
  return false
end

--[[
	@method Fire
	@description Fire an event to all subscribers of the specified type

	Validates the event data, adds it to history, and notifies all subscribers.
	Handles errors gracefully and updates performance metrics.

	@param eventType The type of event to fire
	@param eventData The event data to send to subscribers

	@example
	```lua
	local eventData = EventSystem.CreateEventData(player, "Moderator", "User", "Promotion")
	EventSystem.Fire("ROLE_CHANGED", eventData)
	```
]]
function EventSystem.Fire(eventType: TagEventType, eventData: TagEventData): ()
  -- Validate event type exists
  if not eventSubscribers[eventType] then
    warn(`TBRDS EventSystem: Unknown event type '{eventType}'`)
    return
  end

  -- Validate event data structure
  if not validateEventData(eventType, eventData) then
    warn(`TBRDS EventSystem: Invalid event data for '{eventType}'`)
    eventMetrics.errorCount += 1
    return
  end

  -- Add to historical record
  addToHistory(eventData)

  -- Notify all subscribers for this event type
  local subscriberCount = 0
  for subscriptionId, callback in pairs(eventSubscribers[eventType]) do
    subscriberCount += 1
    safelyInvokeCallback(callback, eventData, subscriptionId)
  end

  -- Update performance metrics
  eventMetrics.eventsProcessed += 1
  eventMetrics.subscribersNotified += subscriberCount

  debugLog(`Fired '{eventType}' event to {subscriberCount} subscribers`)
end

--[[
	@method GetSubscribers
	@description Get all callback functions subscribed to a specific event type

	@param eventType The event type to query
	@return Array of callback functions, empty if no subscribers or invalid event type

	@example
	```lua
	local callbacks = EventSystem.GetSubscribers("ROLE_ASSIGNED")
	print(`Found {#callbacks} subscribers for ROLE_ASSIGNED`)
	```
]]
function EventSystem.GetSubscribers(eventType: TagEventType): { EventCallback }
  if not eventSubscribers[eventType] then
    return {}
  end

  local callbacks = {}
  for _, callback in pairs(eventSubscribers[eventType]) do
    table.insert(callbacks, callback)
  end

  return callbacks
end

--[[
	@method GetEventHistory
	@description Get the complete history of fired events

	History is automatically pruned to maintain performance and memory usage.
	Events are ordered chronologically with newest events at the end.

	@return Array of TagEventData representing historical events

	@example
	```lua
	local history = EventSystem.GetEventHistory()
	for _, event in ipairs(history) do
		print(`{event.Player.Name}: {event.OldRole} -> {event.NewRole}`)
	end
	```
]]
function EventSystem.GetEventHistory(): { TagEventData }
  -- Return a copy to prevent external modification
  local historyCopy = {}
  for _, eventData in ipairs(eventHistory) do
    table.insert(historyCopy, eventData)
  end
  return historyCopy
end

--[[
	@method GetMetrics
	@description Get comprehensive performance and usage metrics

	@return Table containing performance statistics and system health information

	@example
	```lua
	local metrics = EventSystem.GetMetrics()
	print(`Processed {metrics.eventsProcessed} events with {metrics.errorCount} errors`)
	print(`System uptime: {metrics.uptime} seconds`)
	print(`Active subscriptions: {metrics.activeSubscriptions}`)
	```
]]
function EventSystem.GetMetrics()
  -- Calculate active subscriptions count
  local activeSubscriptions = 0
  for _, subscribers in pairs(eventSubscribers) do
    for _ in pairs(subscribers) do
      activeSubscriptions += 1
    end
  end

  return {
    eventsProcessed = eventMetrics.eventsProcessed,
    subscribersNotified = eventMetrics.subscribersNotified,
    errorCount = eventMetrics.errorCount,
    uptime = os.time() - eventMetrics.lastReset,
    activeSubscriptions = activeSubscriptions,
    historySize = #eventHistory,
  }
end

--[[
	@method ResetMetrics
	@description Reset all performance metrics to zero

	Useful for periodic monitoring or after system maintenance.
	Does not affect event history or active subscriptions.

	@example
	```lua
	-- Reset metrics at the start of each hour
	EventSystem.ResetMetrics()
	```
]]
function EventSystem.ResetMetrics(): ()
  eventMetrics.eventsProcessed = 0
  eventMetrics.subscribersNotified = 0
  eventMetrics.errorCount = 0
  eventMetrics.lastReset = os.time()

  debugLog("Event metrics reset")
end

--[[
	@method ClearAllSubscriptions
	@description Remove all event subscriptions across all event types

	WARNING: This will immediately disconnect all event listeners.
	Use with caution, typically only during system shutdown or testing.

	@example
	```lua
	-- Clean shutdown
	EventSystem.ClearAllSubscriptions()
	```
]]
function EventSystem.ClearAllSubscriptions(): ()
  for eventType in pairs(eventSubscribers) do
    eventSubscribers[eventType] = {}
  end

  debugLog("All event subscriptions cleared")
end

--[[
	@method CreateEventData
	@description Helper function to create standardized event data structures

	Ensures consistent event data format and automatically sets timestamp.
	Validates required fields and provides sensible defaults where possible.

	@param player The player associated with this event
	@param newRole The new role being assigned
	@param oldRole The previous role (optional, can be nil)
	@param source Description of what triggered this event
	@param metadata Additional custom data (optional)
	@return Properly formatted TagEventData structure

	@example
	```lua
	-- Basic role assignment
	local eventData = EventSystem.CreateEventData(player, "Admin", nil, "ServerScript")

	-- Role change with metadata
	local eventData = EventSystem.CreateEventData(
		player,
		"Moderator",
		"User",
		"PromotionSystem",
		{ reason = "Excellent behavior", promotedBy = "HeadAdmin" }
	)
	```
]]
function EventSystem.CreateEventData(
  player: Player,
  newRole: string,
  oldRole: string?,
  source: string,
  metadata: { [string]: any }?
): TagEventData
  -- Validate required parameters
  assert(player and player:IsA("Player"), "Player must be a valid Player instance")
  assert(type(newRole) == "string" and newRole ~= "", "NewRole must be a non-empty string")
  assert(type(source) == "string" and source ~= "", "Source must be a non-empty string")

  return {
    Player = player,
    NewRole = newRole :: Types.RoleName,
    OldRole = oldRole,
    Timestamp = os.time(),
    Source = source,
    Metadata = metadata, -- Metadata is optional
  }
end

-- ============================================================================
-- INITIALIZATION & CLEANUP
-- ============================================================================

-- Initialize the event system if enabled in configuration
if TBRDSConfig.Settings.EnableEventSystem then
  debugLog("Event system initialized")

  -- Set up automatic history cleanup on the server
  if RunService:IsServer() then
    task.spawn(function()
      while true do
        task.wait(CLEANUP_INTERVAL)

        -- Remove events older than retention time
        local cutoffTime = os.time() - HISTORY_RETENTION_TIME
        local newHistory = {}

        for _, eventData in ipairs(eventHistory) do
          if eventData.Timestamp > cutoffTime then
            table.insert(newHistory, eventData)
          end
        end

        local removedCount = #eventHistory - #newHistory
        eventHistory = newHistory

        if removedCount > 0 then
          debugLog(
            `Cleaned {removedCount} old events from history, {#eventHistory} events remaining`
          )
        end
      end
    end)
  end
else
  warn("TBRDS EventSystem: Event system is disabled in configuration")
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return EventSystem
