--!strict

--[[
    - file: TBRDS_BILLBOARDSERVICE.LUAU

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Billboard creation and management service for TBRDS.
      - Creates, updates, and cleans up player tag billboards.
      - Handles billboard styling, positioning, and lifecycle.
      - Provides utilities for billboard statistics and performance.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local ConfigurationService = require(script.Parent.TBRDS_ConfigurationService)
local PerformanceMonitor = require(ReplicatedStorage.TBRDS.Shared.TBRDS_PerformanceMonitor)
local Types = require(ReplicatedStorage.TBRDS.TBRDS_Types)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local BillboardService = {}

-- ============================================================================
-- SERVICE STATE
-- ============================================================================

local isInitialized = false
local activeBillboards: { [Player]: BillboardGui } = {}
local billboardSettings: Types.BillboardSettings

-- ============================================================================
-- FONT LOOKUP
-- ============================================================================

-- Safe font lookup table for dynamic font style assignment
local FontLookup: { [string]: Enum.Font } = {
  ["Legacy"] = Enum.Font.Legacy,
  ["Arial"] = Enum.Font.Arial,
  ["ArialBold"] = Enum.Font.ArialBold,
  ["SourceSans"] = Enum.Font.SourceSans,
  ["SourceSansBold"] = Enum.Font.SourceSansBold,
  ["SourceSansItalic"] = Enum.Font.SourceSansItalic,
  ["Bodoni"] = Enum.Font.Bodoni,
  ["Garamond"] = Enum.Font.Garamond,
  ["Cartoon"] = Enum.Font.Cartoon,
  ["Code"] = Enum.Font.Code,
  ["Highway"] = Enum.Font.Highway,
  ["SciFi"] = Enum.Font.SciFi,
  ["Arcade"] = Enum.Font.Arcade,
  ["Fantasy"] = Enum.Font.Fantasy,
  ["Antique"] = Enum.Font.Antique,
  ["Gotham"] = Enum.Font.Gotham,
  ["GothamMedium"] = Enum.Font.GothamMedium,
  ["GothamBold"] = Enum.Font.GothamBold,
  ["GothamBlack"] = Enum.Font.GothamBlack,
  ["AmaticSC"] = Enum.Font.AmaticSC,
  ["PermanentMarker"] = Enum.Font.PermanentMarker,
  ["FredokaOne"] = Enum.Font.FredokaOne,
  ["JosefinSans"] = Enum.Font.JosefinSans,
  ["Nunito"] = Enum.Font.Nunito,
  ["Oswald"] = Enum.Font.Oswald,
  ["Roboto"] = Enum.Font.Roboto,
  ["RobotoCondensed"] = Enum.Font.RobotoCondensed,
  ["Ubuntu"] = Enum.Font.Ubuntu,
  ["LuckiestGuy"] = Enum.Font.LuckiestGuy,
  ["TitilliumWeb"] = Enum.Font.TitilliumWeb,
  ["IndieFlower"] = Enum.Font.IndieFlower,
  ["Bangers"] = Enum.Font.Bangers,
  ["Creepster"] = Enum.Font.Creepster,
  ["PatrickHand"] = Enum.Font.PatrickHand,
  ["SpecialElite"] = Enum.Font.SpecialElite,
}

-- ============================================================================
-- DEBUG LOGGING
-- ============================================================================

--[[
  Print debug messages if debug mode is enabled.
  @param message: string -- Message to print.
]]
local function debugLog(message: string)
  if ConfigurationService.IsDebugMode() then
    print(string.format("[TBRDS:BillboardService]: %s", message))
  end
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

--[[
  Initialize the billboard service.
  Loads settings and sets up cleanup on player removal.
  @return boolean -- True if initialized, false if already initialized.
]]
function BillboardService.Initialize(): boolean
  if isInitialized then
    debugLog("Billboard service already initialized")
    return true
  end

  debugLog("Initializing billboard service...")

  -- Load billboard settings from configuration
  billboardSettings = ConfigurationService.GetBillboardSettings()

  -- Set up player cleanup
  Players.PlayerRemoving:Connect(function(player)
    BillboardService.RemoveBillboard(player)
  end)

  isInitialized = true
  debugLog("Billboard service initialized successfully")
  return true
end

-- ============================================================================
-- BILLBOARD CREATION & POPULATION
-- ============================================================================

--[[
  Create a billboard for a player with the given role and style.
  @param player Player -- The player to create a billboard for.
  @param roleName string -- The role name to display.
  @param style RoleStyle -- The style to apply.
  @return BillboardGui? -- The created BillboardGui, or nil on failure.
]]
function BillboardService.CreateBillboard(
  player: Player,
  roleName: string,
  style: Types.RoleStyle
): BillboardGui?
  if not player.Character then
    debugLog(string.format("Cannot create billboard for %s: no character", player.Name))
    return nil
  end

  local head = player.Character:FindFirstChild("Head")
  if not head then
    debugLog(string.format("Cannot create billboard for %s: no head", player.Name))
    return nil
  end

  -- Remove existing billboard if present
  BillboardService.RemoveBillboard(player)

  -- Create new billboard
  local billboard = Instance.new("BillboardGui")
  billboard.Name = "TagGui"
  billboard.Adornee = head
  billboard.Size = billboardSettings.Size
  billboard.StudsOffset = billboardSettings.StudsOffset
  billboard.AlwaysOnTop = billboardSettings.AlwaysOnTop
  billboard.MaxDistance = billboardSettings.MaxDistance
  billboard.Active = true
  billboard.ZIndexBehavior = Enum.ZIndexBehavior.Global
  billboard.LightInfluence = billboardSettings.LightInfluence
  billboard.Parent = head

  -- Create content
  local success = BillboardService.PopulateBillboard(billboard, player, roleName, style)

  if not success then
    billboard:Destroy()
    return nil
  end

  -- Store reference
  activeBillboards[player] = billboard

  debugLog(string.format("Created billboard for %s with role %s", player.Name, roleName))
  PerformanceMonitor.RecordTagAssignment(roleName)

  return billboard
end

--[[
  Populate a billboard with tag content and style.
  @param billboard BillboardGui -- The billboard to populate.
  @param player Player -- The player for whom the billboard is created.
  @param roleName string -- The role name to display.
  @param style RoleStyle -- The style to apply.
  @return boolean -- True if successful, false otherwise.
]]
function BillboardService.PopulateBillboard(
  billboard: BillboardGui,
  player: Player,
  roleName: string,
  style: Types.RoleStyle
): boolean
  local success = pcall(function()
    -- Create a frame to hold the tag elements
    local frame = Instance.new("Frame")
    frame.Name = "TagFrame"
    frame.Size = UDim2.new(1, 0, 1, 0)
    frame.BackgroundTransparency = 1
    frame.Parent = billboard

    -- Add UIListLayout for horizontal arrangement
    local layout = Instance.new("UIListLayout")
    layout.FillDirection = Enum.FillDirection.Horizontal
    layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
    layout.VerticalAlignment = Enum.VerticalAlignment.Center
    layout.SortOrder = Enum.SortOrder.LayoutOrder
    layout.Padding = UDim.new(0, 10) -- 10-pixel gap between image and text
    layout.Parent = frame

    -- Add icon if specified
    if style.Image then
      local icon = Instance.new("ImageLabel")
      icon.Name = "Icon"
      icon.Size = UDim2.new(0, 30, 0, 30) -- Fixed size for the image
      icon.BackgroundTransparency = 1
      icon.Image = style.Image
      icon.LayoutOrder = 1
      icon.ImageTransparency = 0
      icon.ScaleType = Enum.ScaleType.Fit
      icon.AnchorPoint = Vector2.new(0.5, 0.5)
      icon.Position = UDim2.new(0.5, 0, 0.5, 0)
      icon.Parent = frame

      debugLog(string.format("Added icon for %s: %s", player.Name, style.Image))
    end

    -- Create the tag label
    local label = Instance.new("TextLabel")
    label.Name = "TagLabel"
    label.Size = UDim2.new(0, 150, 0, 30) -- Fixed width, adjust as needed
    label.BackgroundTransparency = 1
    label.Text = style.GetText and style.GetText(player) or "[" .. roleName .. "]"
    label.TextColor3 = style.Color
    label.Font = style.Font
    label.TextScaled = true
    label.TextStrokeTransparency = 0.5
    label.LayoutOrder = 2
    label.Parent = frame

    -- Apply gradient if specified
    if style.Gradient then
      BillboardService.ApplyGradient(label, style.Gradient)
    end

    -- Apply text effects if specified
    if style.TextStroke then
      label.TextStrokeColor3 = style.TextStroke.Color
      label.TextStrokeTransparency = style.TextStroke.Transparency
    end

    -- Apply font style if specified
    if style.FontStyle then
      local fontEnum = FontLookup[style.FontStyle]
      if fontEnum then
        label.Font = fontEnum
      end
    end
  end)

  if not success then
    warn(string.format("TBRDS: Failed to populate billboard for %s", player.Name))
    PerformanceMonitor.RecordError("BILLBOARD_POPULATION_ERROR")
    return false
  end

  return true
end

--[[
  Apply a gradient to a text label.
  @param label TextLabel -- The label to apply the gradient to.
  @param gradientStyle GradientStyle -- The gradient style.
]]
function BillboardService.ApplyGradient(label: TextLabel, gradientStyle: Types.GradientStyle): ()
  local gradient = Instance.new("UIGradient")

  if #gradientStyle.Colors == 2 then
    local keypoints = {
      ColorSequenceKeypoint.new(0, gradientStyle.Colors[1]),
      ColorSequenceKeypoint.new(1, gradientStyle.Colors[2]),
    }
    gradient.Color = ColorSequence.new(keypoints)
  else
    local keypoints = {}
    for i, color in ipairs(gradientStyle.Colors) do
      local position = (i - 1) / (#gradientStyle.Colors - 1)
      table.insert(keypoints, ColorSequenceKeypoint.new(position, color))
    end
    gradient.Color = ColorSequence.new(keypoints)
  end

  gradient.Rotation = gradientStyle.Rotation
  gradient.Parent = label
end

-- ============================================================================
-- BILLBOARD UPDATING & REMOVAL
-- ============================================================================

--[[
    Update an existing billboard for a player.
    @param player Player -- The player whose billboard to update.
    @param roleName string -- The new role name.
    @param style RoleStyle -- The new style.
    @return boolean -- True if updated, false otherwise.
]]
function BillboardService.UpdateBillboard(
  player: Player,
  roleName: string,
  style: Types.RoleStyle
): boolean
  local existingBillboard = activeBillboards[player]

  if not existingBillboard then
    -- Create new billboard if none exists
    local newBillboard = BillboardService.CreateBillboard(player, roleName, style)
    return newBillboard ~= nil
  end

  -- Check if billboard still exists in the game
  if not existingBillboard.Parent then
    activeBillboards[player] = nil
    local newBillboard = BillboardService.CreateBillboard(player, roleName, style)
    return newBillboard ~= nil
  end

  -- Remove existing frame if it exists
  local frame = existingBillboard:FindFirstChild("TagFrame") :: Frame?
  if frame then
    frame:Destroy()
  end

  -- Repopulate with new content
  local success = BillboardService.PopulateBillboard(existingBillboard, player, roleName, style)
  if success then
    debugLog(string.format("Updated billboard for %s with role %s", player.Name, roleName))
  end

  return success
end

--[[
  Remove a player's billboard.
  @param player Player -- The player whose billboard to remove.
]]
function BillboardService.RemoveBillboard(player: Player): ()
  local billboard = activeBillboards[player]
  if billboard then
    billboard:Destroy()
    activeBillboards[player] = nil
    debugLog(string.format("Removed billboard for %s", player.Name))
  end
end

--[[
  Get a player's current billboard.
  @param player Player -- The player to query.
  @return BillboardGui? -- The player's billboard, or nil if none.
]]
function BillboardService.GetBillboard(player: Player): BillboardGui?
  return activeBillboards[player]
end

--[[
  Check if a player has a billboard.
  @param player Player -- The player to check.
  @return boolean -- True if the player has a billboard, false otherwise.
]]
function BillboardService.HasBillboard(player: Player): boolean
  local billboard = activeBillboards[player]
  if billboard then
    return billboard.Parent ~= nil
  end
  return false
end

-- ============================================================================
-- BILLBOARD SETTINGS & UTILITIES
-- ============================================================================

--[[
  Update billboard settings for all active billboards.
  @param newSettings BillboardSettings -- The new settings to apply.
]]
function BillboardService.UpdateBillboardSettings(newSettings: Types.BillboardSettings): ()
  billboardSettings = newSettings

  -- Update all existing billboards
  for _player, billboard in pairs(activeBillboards) do
    if billboard and billboard.Parent then
      billboard.Size = billboardSettings.Size
      billboard.StudsOffset = billboardSettings.StudsOffset
      billboard.AlwaysOnTop = billboardSettings.AlwaysOnTop
      billboard.MaxDistance = billboardSettings.MaxDistance
      billboard.LightInfluence = billboardSettings.LightInfluence
    end
  end

  debugLog("Updated billboard settings for all active billboards")
end

--[[
  Get statistics about active and valid billboards.
  @return {[string]: any} -- Table with statistics.
]]
function BillboardService.GetBillboardStatistics(): { [string]: any }
  local stats = {
    activeBillboards = 0,
    validBillboards = 0,
    invalidBillboards = 0,
  }

  for _player, billboard in pairs(activeBillboards) do
    stats.activeBillboards = stats.activeBillboards + 1

    if billboard and billboard.Parent then
      stats.validBillboards = stats.validBillboards + 1
    else
      stats.invalidBillboards = stats.invalidBillboards + 1
    end
  end

  return stats
end

--[[
  Clean up invalid billboards (orphans, destroyed, or player left).
  @return number -- Number of billboards removed.
]]
function BillboardService.CleanupInvalidBillboards(): number
  local removedCount = 0

  for player, billboard in pairs(activeBillboards) do
    if not billboard or not billboard.Parent or not player.Parent then
      activeBillboards[player] = nil
      removedCount = removedCount + 1
    end
  end

  if removedCount > 0 then
    debugLog(string.format("Cleaned up %d invalid billboards", removedCount))
  end

  return removedCount
end

--[[
  Get all active billboards.
  @return {[Player]: BillboardGui} -- Table of active billboards.
]]
function BillboardService.GetActiveBillboards(): { [Player]: BillboardGui }
  return activeBillboards
end

--[[
    Get the current status of the billboard service.
    @return BillboardServiceStatus -- Table with status and statistics.
]]
function BillboardService.GetServiceStatus(): Types.BillboardServiceStatus
  local stats = BillboardService.GetBillboardStatistics()

  return {
    initialized = isInitialized,
    activeBillboards = stats.activeBillboards,
    validBillboards = stats.validBillboards,
    invalidBillboards = stats.invalidBillboards,
    settings = billboardSettings,
  }
end

--[[
  Cleanup the billboard service and remove all billboards.
]]
function BillboardService.Cleanup(): ()
  -- Remove all billboards
  for player in pairs(activeBillboards) do
    BillboardService.RemoveBillboard(player)
  end

  activeBillboards = {}
  isInitialized = false
  debugLog("Billboard service cleaned up")
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return BillboardService
