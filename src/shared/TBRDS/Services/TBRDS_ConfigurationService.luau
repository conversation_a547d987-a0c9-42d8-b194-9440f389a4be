--!strict

--[[
    - file: TBRDS_ConfigurationService.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Centralized configuration management service for TBRDS.
      - Manages all system configuration and settings.
      - Provides runtime configuration updates and notifications.
      - Handles environment-specific configurations and feature flags.
      - Validates configuration integrity.
]]

-- ==============================================================================
-- SERVICES
-- ==============================================================================

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ==============================================================================
-- MODULES
-- ==============================================================================

local EventSystem = require(ReplicatedStorage.TBRDS.Shared.TBRDS_EventSystem)
local TBRDSConfig = require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
local Types = require(ReplicatedStorage.TBRDS.TBRDS_Types)

-- =============================================================================
-- TYPES
-- =============================================================================

-- TBRDS Configuration type
type TBRDSConfiguration = Types.TBRDSConfiguration

-- Configuration listener type
type ConfigurationListener = (Types.TBRDSConfiguration) -> ()

-- Configuration service type
type ConfigurationServiceType = {
  Initialize: () -> boolean,
  GetConfiguration: () -> Types.TBRDSConfiguration,
  GetSettings: () -> Types.TBRDSSettings,
  GetGroups: () -> { [string]: any }, -- Broad type, as Groups has dynamic keys
  GetRolePriority: () -> { string },
  GetGamePasses: () -> { [string]: number },
  GetEvents: () -> Types.EventNames,
  GetRemotes: () -> { [string]: string },
  GetErrorCodes: () -> { [string]: string },
  IsDebugMode: () -> boolean,
  IsPerformanceMetricsEnabled: () -> boolean,
  IsEventSystemEnabled: () -> boolean,
  GetRateLimitConfig: () -> { Window: number, MaxRequests: number },
  GetBillboardSettings: () -> Types.BillboardSettings,
  GetAntiExploitSettings: () -> Types.AntiExploitSettings,
  UpdateConfiguration: (newConfig: Types.TBRDSConfiguration) -> boolean,
  SubscribeToChanges: (callback: ConfigurationListener) -> string,
  UnsubscribeFromChanges: (listenerId: string) -> boolean,
  ValidateConfiguration: (config: Types.TBRDSConfiguration) -> (boolean, { string }),
  GetConfigurationSummary: () -> Types.ConfigurationSummary,
  ReloadConfiguration: () -> boolean,
  Cleanup: () -> (),
}

-- =============================================================================
-- MAIN IMPLEMENTATION
-- =============================================================================
local ConfigurationService = {}

-- ==============================================================================
-- MODULE STATES
-- ==============================================================================
local isInitialized = false
local currentConfig: TBRDSConfiguration = TBRDSConfig
local configurationListeners: { [string]: (TBRDSConfiguration) -> () } = {}
local listenerCounter = 0

-- ==============================================================================
-- HELPER FUNCTIONS
-- ==============================================================================
-- Debug logging
--[[
  Print debug messages if debug mode is enabled.
  @param message string -- Message to print.
]]
local function debugLog(message: string)
  if ConfigurationService.IsDebugMode() then
    print(string.format("[TBRDS:ConfigurationService]: %s", message))
  end
end

--[[
  Initialize the configuration service.
  Loads and validates configuration.
  @return boolean -- True if initialized, false otherwise.
]]
function ConfigurationService.Initialize(): boolean
  if isInitialized then
    debugLog("Configuration service already initialized")
    return true
  end

  debugLog("Initializing configuration service...")

  -- Validate configuration
  local isValid, errors = ConfigurationService.ValidateConfiguration(currentConfig)
  if not isValid then
    warn("TBRDS: Configuration validation failed:")
    for _, error in ipairs(errors) do
      warn("  " .. error)
    end
    return false
  end

  isInitialized = true
  debugLog("Configuration service initialized successfully")
  return true
end

--[[
  Get the current configuration.
  @return TBRDSConfiguration -- The current configuration table.
]]
function ConfigurationService.GetConfiguration(): TBRDSConfiguration
  return currentConfig
end

--[[
  Get the settings section of the configuration.
  @return any -- The settings section.
]]
function ConfigurationService.GetSettings()
  return currentConfig.Settings
end

--[[
  Get the groups section of the configuration.
  @return any -- The groups section.
]]
function ConfigurationService.GetGroups()
  return currentConfig.Groups
end

--[[
  Get the role priority array.
  @return {string} -- The role priority array.
]]
function ConfigurationService.GetRolePriority(): { string }
  return currentConfig.RolePriority
end

--[[
  Get the game passes section.
  @return any -- The game passes section.
]]
function ConfigurationService.GetGamePasses()
  return currentConfig.GamePasses
end

--[[
  Get the events section.
  @return any -- The events section.
]]
function ConfigurationService.GetEvents()
  return currentConfig.Events
end

--[[
  Get the remotes section.
  @return any -- The remotes section.
]]
function ConfigurationService.GetRemotes()
  return currentConfig.Remotes
end

--[[
  Get the error codes section.
  @return any -- The error codes section.
]]
function ConfigurationService.GetErrorCodes()
  return currentConfig.ErrorCodes
end

--[[
  Check if debug mode is enabled.
  @return boolean -- True if debug mode is enabled.
]]
function ConfigurationService.IsDebugMode(): boolean
  return currentConfig.Settings.DebugMode
end

--[[
  Check if performance metrics are enabled.
  @return boolean -- True if enabled.
]]
function ConfigurationService.IsPerformanceMetricsEnabled(): boolean
  return currentConfig.Settings.EnablePerformanceMetrics
end

--[[
  Check if event system is enabled.
  @return boolean -- True if enabled.
]]
function ConfigurationService.IsEventSystemEnabled(): boolean
  return currentConfig.Settings.EnableEventSystem == true
end

--[[
  Get rate limiting configuration.
  @return any -- The rate limit configuration.
]]
function ConfigurationService.GetRateLimitConfig()
  return currentConfig.Settings.RateLimit
end

--[[
  Get billboard settings from configuration.
  @return any -- The billboard settings.
]]
function ConfigurationService.GetBillboardSettings()
  return currentConfig.Settings.BillboardSettings
end

--[[
  Get anti-exploit settings from configuration.
  @return any -- The anti-exploit settings.
]]
function ConfigurationService.GetAntiExploitSettings()
  return currentConfig.Settings.AntiExploit
end

--[[
    Update the configuration (server-side only).
    Validates and applies the new configuration, notifies listeners.
    @param newConfig TBRDSConfiguration -- The new configuration to apply.
    @return boolean -- True if updated, false otherwise.
]]
function ConfigurationService.UpdateConfiguration(newConfig: TBRDSConfiguration): boolean
  if not RunService:IsServer() then
    warn("TBRDS: Configuration updates can only be performed on the server")
    return false
  end

  -- Validate new configuration
  local isValid, errors = ConfigurationService.ValidateConfiguration(newConfig)
  if not isValid then
    warn("TBRDS: New configuration validation failed:")
    for _, error in ipairs(errors) do
      warn("  " .. error)
    end
    return false
  end

  local oldConfig = currentConfig
  currentConfig = newConfig

  -- Notify listeners
  for listenerId, callback in pairs(configurationListeners) do
    local success = pcall(function()
      callback(currentConfig)
    end)
    if not success then
      warn(string.format("TBRDS: Error in configuration listener '%s'", listenerId))
    end
  end

  -- Fire configuration changed event
  if ConfigurationService.IsEventSystemEnabled() then
    local eventData = EventSystem.CreateEventData(
      game.Players.LocalPlayer or game.Players:GetPlayers()[1], -- Fallback for server
      "ConfigurationChanged",
      "ConfigurationUpdated",
      "ConfigurationService.UpdateConfiguration",
      {
        oldConfig = oldConfig,
        newConfig = newConfig,
      }
    )
    EventSystem.Fire("ConfigurationChanged", eventData)
  end

  debugLog("Configuration updated successfully")
  return true
end

--[[
  Subscribe to configuration changes.
  @param callback (TBRDSConfiguration) -> () -- Callback to invoke on changes.
  @return string -- Listener ID.
]]
function ConfigurationService.SubscribeToChanges(callback: (TBRDSConfiguration) -> ()): string
  listenerCounter = listenerCounter + 1
  local listenerId = string.format("config_listener_%d", listenerCounter)
  configurationListeners[listenerId] = callback
  debugLog(string.format("Configuration listener '%s' registered", listenerId))
  return listenerId
end

--[[
  Unsubscribe from configuration changes.
  @param listenerId string -- The listener ID to remove.
  @return boolean -- True if removed, false otherwise.
]]
function ConfigurationService.UnsubscribeFromChanges(listenerId: string): boolean
  if configurationListeners[listenerId] then
    configurationListeners[listenerId] = nil
    debugLog(string.format("Configuration listener '%s' unregistered", listenerId))
    return true
  end
  return false
end

--[[
  Validate configuration integrity.
  @param config TBRDSConfiguration -- The configuration to validate.
  @return (boolean, {string}) -- True if valid, array of error messages if not.
]]
function ConfigurationService.ValidateConfiguration(
  config: TBRDSConfiguration
): (boolean, { string })
  local errors = {}

  -- Validate settings
  if not config.Settings then
    table.insert(errors, "Missing Settings section")
  else
    if type(config.Settings.DebugMode) ~= "boolean" then
      table.insert(errors, "Settings.DebugMode must be a boolean")
    end

    if type(config.Settings.EnablePerformanceMetrics) ~= "boolean" then
      table.insert(errors, "Settings.EnablePerformanceMetrics must be a boolean")
    end

    if not config.Settings.RateLimit then
      table.insert(errors, "Missing Settings.RateLimit section")
    else
      if
        type(config.Settings.RateLimit.Window) ~= "number"
        or config.Settings.RateLimit.Window <= 0
      then
        table.insert(errors, "Settings.RateLimit.Window must be a positive number")
      end

      if
        type(config.Settings.RateLimit.MaxRequests) ~= "number"
        or config.Settings.RateLimit.MaxRequests <= 0
      then
        table.insert(errors, "Settings.RateLimit.MaxRequests must be a positive number")
      end
    end
  end

  -- Validate groups
  if not config.Groups or not config.Groups.Primary then
    table.insert(errors, "Missing Groups.Primary section")
  else
    if type(config.Groups.Primary.Id) ~= "number" or config.Groups.Primary.Id <= 0 then
      table.insert(errors, "Groups.Primary.Id must be a positive number")
    end

    if type(config.Groups.Primary.Name) ~= "string" or config.Groups.Primary.Name == "" then
      table.insert(errors, "Groups.Primary.Name must be a non-empty string")
    end
  end

  -- Validate role priority
  if
    not config.RolePriority
    or type(config.RolePriority) ~= "table"
    or #config.RolePriority == 0
  then
    table.insert(errors, "RolePriority must be a non-empty array")
  end

  -- Validate remotes
  if not config.Remotes then
    table.insert(errors, "Missing Remotes section")
  else
    local requiredRemotes = { "TagUpdate", "TagRequest", "TagValidation", "SecurityReport" }
    for _, remoteName in ipairs(requiredRemotes) do
      if not config.Remotes[remoteName] or type(config.Remotes[remoteName]) ~= "string" then
        table.insert(errors, string.format("Missing or invalid Remotes.%s", remoteName))
      end
    end
  end

  return #errors == 0, errors
end

--[[
    Get a summary of the current configuration for debugging.
    @return ConfigurationSummary -- Table with summary information.
]]
function ConfigurationService.GetConfigurationSummary(): Types.ConfigurationSummary
  return {
    initialized = isInitialized,
    debugMode = ConfigurationService.IsDebugMode(),
    performanceMetrics = ConfigurationService.IsPerformanceMetricsEnabled(),
    eventSystem = ConfigurationService.IsEventSystemEnabled(),
    rateLimitWindow = currentConfig.Settings.RateLimit.Window,
    rateLimitMaxRequests = currentConfig.Settings.RateLimit.MaxRequests,
    primaryGroupId = currentConfig.Groups.Primary.Id,
    roleCount = #currentConfig.RolePriority,
    listenerCount = (function()
      local count = 0
      for _ in pairs(configurationListeners) do
        count = count + 1
      end
      return count
    end)(),
  }
end

--[[
  Reload configuration from source (server-side only).
  @return boolean -- True if reloaded, false otherwise.
]]
function ConfigurationService.ReloadConfiguration(): boolean
  if not RunService:IsServer() then
    warn("TBRDS: Configuration reload can only be performed on the server")
    return false
  end

  debugLog("Reloading configuration from source...")

  -- Reload the configuration module
  local success, newConfigOrError = pcall(function()
    -- Force reload by clearing the module cache
    return require(ReplicatedStorage.Configurations.Systems.TBRDS_Configuration)
  end)

  if not success then
    warn("TBRDS: Failed to reload configuration: " .. tostring(newConfigOrError))
    return false
  end

  -- newConfigOrError is the new config if success
  return ConfigurationService.UpdateConfiguration(newConfigOrError)
end

--[[
  Cleanup the configuration service and remove all listeners.
]]
function ConfigurationService.Cleanup(): ()
  configurationListeners = {}
  isInitialized = false
  debugLog("Configuration service cleaned up")
end

return ConfigurationService :: ConfigurationServiceType
