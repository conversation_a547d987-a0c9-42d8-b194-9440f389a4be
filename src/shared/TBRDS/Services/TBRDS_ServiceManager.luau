--!strict

--[[
  - file: TBRDS_ServiceManager.luau

  - version: 2.0.0
  - author: BleckWolf25
  - contributors:

  - description:
    - Service Manager for TBRDS, responsible for initializing, managing, and monitoring services.
    - Provides health checks, periodic validation, and emergency recovery.
    - Supports debug logging and performance monitoring.
    - Handles service dependencies and initialization order.
    - Provides a summary of service statuses and system health.
]]

-- =============================================================================
-- SERVICES
-- =============================================================================

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- =============================================================================
-- PATHS
-- =============================================================================

local ServiceModules = script.Parent
local ReplicatedTBRDS = ReplicatedStorage.TBRDS

-- =============================================================================
-- MODULE SERVICES
-- =============================================================================

local BillboardService = require(ServiceModules.TBRDS_BillboardService)
local ConfigurationService = require(ServiceModules.TBRDS_ConfigurationService)
local RoleService = require(ServiceModules.TBRDS_RoleService)
local TagService = require(ServiceModules.TBRDS_TagService)

-- =============================================================================
-- MODULES
-- =============================================================================

local EventSystem = require(ReplicatedTBRDS.Shared.TBRDS_EventSystem)
local PerformanceMonitor = require(ReplicatedTBRDS.Shared.TBRDS_PerformanceMonitor)
local Types = require(ReplicatedTBRDS.TBRDS_Types)

-- =============================================================================
-- TYPE ANNOTATIONS & DEFINITIONS
-- =============================================================================

type ServiceModule = {
  Initialize: () -> boolean,
  Cleanup: (() -> ())?,
  GetServiceStatus: (() -> { initialized: boolean })?,
  RefreshPlayerTag: ((Player) -> Types.ValidationResult)?,
}

local services: { [string]: ServiceModule } = {
  Configuration = ConfigurationService :: ServiceModule,
  Role = RoleService :: ServiceModule,
  Billboard = BillboardService :: ServiceModule,
  Tag = TagService :: ServiceModule,
}

-- =============================================================================
-- SERVICE STATES
-- =============================================================================

local isInitialized = false
local initializationOrder = { "Configuration", "Role", "Billboard", "Tag" }
local serviceStatus: { [string]: { initialized: boolean, healthy: boolean, lastCheck: number } } =
  {}
local healthCheckInterval = 30 -- seconds

-- =============================================================================
-- DEBUG LOGGING
-- =============================================================================

-- Debug log
local function debugLog(message: string)
  if ConfigurationService.IsDebugMode() then
    print(string.format("[TBRDS:ServiceManager]: %s", message))
  end
end

-- =============================================================================
-- MAIN IMPLEMENTATION
-- =============================================================================

local ServiceManager = {}

-- =============================================================================
-- INITIALIZATION
-- =============================================================================

-- Initialize Service Manager
function ServiceManager.Initialize(): boolean
  if isInitialized then
    debugLog("Service manager already initialized")
    return true
  end

  debugLog("Initializing TBRDS Service Manager...")

  -- Initialize EventSystem (ensure loaded and clean metrics)
  debugLog("Initializing TBRDS EventSystem...")
  if EventSystem.ResetMetrics then
    EventSystem.ResetMetrics()
  end

  -- Initialize services in dependency order
  for _, serviceName in ipairs(initializationOrder) do
    local service = services[serviceName]
    if not service or type(service) ~= "table" or type(service.Initialize) ~= "function" then
      warn(string.format("TBRDS: Service '%s' not found or missing Initialize", serviceName))
      return false
    end
    debugLog(string.format("Initializing %s service...", serviceName))
    local success = service.Initialize()
    if not success then
      error(string.format("TBRDS: Failed to initialize %s service", serviceName))
      return false
    end
    serviceStatus[serviceName] = {
      initialized = true,
      healthy = true,
      lastCheck = os.time(),
    }
    debugLog(string.format("%s service initialized successfully", serviceName))
  end

  -- Start health monitoring
  ServiceManager.StartHealthMonitoring()

  -- Set up periodic validation
  ServiceManager.StartPeriodicValidation()

  isInitialized = true
  debugLog("TBRDS Service Manager initialized successfully")

  return true
end

-- =============================================================================
-- SERVICE FUNCTIONS
-- =============================================================================

-- Get service
function ServiceManager.GetService(serviceName: string): ServiceModule?
  local service = services[serviceName]
  if not service then
    warn(string.format("TBRDS: Service '%s' not found", serviceName))
    return nil
  end

  local status = serviceStatus[serviceName]
  if not status then
    warn(string.format("TBRDS: Service '%s' not found in serviceStatus", serviceName))
    return nil
  elseif not status.initialized then
    warn(string.format("TBRDS: Service '%s' not initialized", serviceName))
    return nil
  end

  return service
end

-- Function to get all services
function ServiceManager.GetAllServices(): { [string]: ServiceModule }
  return services
end

-- Function to Check if all Services are initialized
function ServiceManager.AreAllServicesInitialized(): boolean
  for serviceName in pairs(services) do
    local status = serviceStatus[serviceName]
    if not status or not status.initialized then
      return false
    end
  end
  return true
end

-- Function to Check if all Services are healthy
function ServiceManager.AreAllServicesHealthy(): boolean
  for serviceName in pairs(services) do
    local status = serviceStatus[serviceName]
    if not status or not status.healthy then
      return false
    end
  end
  return true
end

-- Start Health Monitoring
function ServiceManager.StartHealthMonitoring(): ()
  if not RunService:IsServer() then
    return
  end

  task.spawn(function()
    while isInitialized do
      task.wait(healthCheckInterval)
      ServiceManager.PerformHealthCheck()
    end
  end)

  debugLog("Health monitoring started")
end

-- Perform Health Check
function ServiceManager.PerformHealthCheck(): ()
  for serviceName, service in pairs(services) do
    local status = serviceStatus[serviceName]
    if status and status.initialized then
      local isHealthy = ServiceManager.CheckServiceHealth(serviceName, service)
      status.healthy = isHealthy
      status.lastCheck = os.time()

      if not isHealthy then
        warn(string.format("TBRDS: Service '%s' health check failed", serviceName))
        PerformanceMonitor.RecordError("SERVICE_HEALTH_CHECK_FAILED")
      end
    end
  end
end

-- Check Service Health
function ServiceManager.CheckServiceHealth(_serviceName: string, service: ServiceModule): boolean
  local success, result = pcall(function()
    if service.GetServiceStatus then
      local status = service.GetServiceStatus()
      return status and status.initialized == true
    end
    return true -- Service exists, assume healthy if no status check
  end)

  if success then
    return result :: boolean
  else
    return false
  end
end

-- Start Periodic Validation
function ServiceManager.StartPeriodicValidation(): ()
  if not RunService:IsServer() then
    return
  end

  task.spawn(function()
    while isInitialized do
      task.wait(60) -- Every minute
      local tagService = ServiceManager.GetService("Tag")
      if tagService and tagService.RefreshPlayerTag then
        local players = game.Players:GetPlayers()
        for _, player in ipairs(players) do
          if player.Parent then
            tagService.RefreshPlayerTag(player)
          end
        end
      end
    end
  end)

  debugLog("Periodic validation started")
end

-- Get Service Status Summary
function ServiceManager.GetServiceStatusSummary(): {
  managerInitialized: boolean,
  totalServices: number,
  initializedServices: number,
  healthyServices: number,
  services: { [string]: Types.ServiceStatus },
}
  local summary: {
    managerInitialized: boolean,
    totalServices: number,
    initializedServices: number,
    healthyServices: number,
    services: { [string]: Types.ServiceStatus },
  } =
    {
      managerInitialized = isInitialized,
      totalServices = 0,
      initializedServices = 0,
      healthyServices = 0,
      services = {},
    }

  for serviceName, service in pairs(services) do
    summary.totalServices = summary.totalServices + 1
    local status = serviceStatus[serviceName]
    local serviceInfo: Types.ServiceStatus = {
      initialized = status and status.initialized or false,
      healthy = status and status.healthy or false,
      lastCheck = status and status.lastCheck or 0,
      details = nil,
    }
    if service and service.GetServiceStatus then
      local ok, detailedStatus = pcall(service.GetServiceStatus)
      if ok and detailedStatus then
        serviceInfo.details = detailedStatus :: { [string]: any }
      end
    end
    summary.services[serviceName] = serviceInfo
    if serviceInfo.initialized then
      summary.initializedServices = summary.initializedServices + 1
    end
    if serviceInfo.healthy then
      summary.healthyServices = summary.healthyServices + 1
    end
  end

  return summary
end

-- Get system health report
function ServiceManager.GetSystemHealthReport(): string
  local summary: {
    managerInitialized: boolean,
    totalServices: number,
    initializedServices: number,
    healthyServices: number,
    services: {
      [string]: {
        initialized: boolean,
        healthy: boolean,
        lastCheck: number,
        details: { [string]: any }?,
      },
    },
  } =
    ServiceManager.GetServiceStatusSummary()
  local report = {
    "=== TBRDS System Health Report ===",
    string.format(
      "Manager Initialized: %s",
      tostring(summary.managerInitialized and "✅" or "❌")
    ),
    string.format(
      "Services: %d/%d initialized, %d/%d healthy",
      summary.initializedServices,
      summary.totalServices,
      summary.healthyServices,
      summary.totalServices
    ),
    "",
    "=== Service Details ===",
  }

  for serviceName: string, serviceInfo in pairs(summary.services) do
    local statusIcon = serviceInfo.healthy and "✅" or "❌"
    local initIcon = serviceInfo.initialized and "✅" or "❌"
    table.insert(
      report,
      string.format(
        "%s %s Service - Init: %s, Healthy: %s",
        statusIcon,
        serviceName,
        initIcon,
        serviceInfo.healthy and "Yes" or "No"
      )
    )
    if serviceInfo.details and type(serviceInfo.details) == "table" then
      for key, value in pairs(serviceInfo.details) do
        if value ~= nil and type(value) ~= "table" then
          table.insert(report, string.format("  %s: %s", key, tostring(value)))
        end
      end
    end
  end

  -- Add performance metrics
  table.insert(report, "")
  table.insert(report, "=== Performance Metrics ===")
  local metrics: Types.PerformanceMetrics = PerformanceMonitor.GetDetailedMetrics()
  table.insert(report, string.format("Tag Assignments: %d", metrics.basic.TagAssignments))
  table.insert(report, string.format("Security Events: %d", metrics.basic.SecurityEvents))
  table.insert(report, string.format("Cache Hits: %d", metrics.basic.CacheHits))
  table.insert(report, string.format("Cache Misses: %d", metrics.basic.CacheMisses))
  table.insert(report, string.format("Errors: %d", metrics.basic.ErrorCount))

  return table.concat(report, "\n")
end

-- =============================================================================
-- SERVICE SYSTEM FUNCTIONS
-- =============================================================================

-- Restart Service
function ServiceManager.RestartService(serviceName: string): boolean
  local service = services[serviceName]
  if not service or not service.Cleanup then
    warn(string.format("TBRDS: Service '%s' not found or missing Cleanup", serviceName))
    return false
  end
  debugLog(string.format("Restarting %s service...", serviceName))
  local ok = pcall(service.Cleanup)
  if not ok then
    warn(string.format("TBRDS: Error during %s service cleanup", serviceName))
  end
  local reinitSuccess = service.Initialize()
  if reinitSuccess then
    serviceStatus[serviceName] = {
      initialized = true,
      healthy = true,
      lastCheck = os.time(),
    }
    debugLog(string.format("%s service restarted successfully", serviceName))
  else
    warn(string.format("TBRDS: Failed to restart %s service", serviceName))
    serviceStatus[serviceName] = {
      initialized = false,
      healthy = false,
      lastCheck = os.time(),
    }
  end
  return reinitSuccess
end

-- Shutdown Service
function ServiceManager.Shutdown(): ()
  debugLog("Shutting down TBRDS Service Manager...")

  -- Cleanup services in reverse order
  local reverseOrder: { string } = {}
  for i = #initializationOrder, 1, -1 do
    table.insert(reverseOrder, initializationOrder[i])
  end

  for _, serviceName in ipairs(reverseOrder) do
    local service = services[serviceName]
    if service and service.Cleanup then
      debugLog(string.format("Cleaning up %s service...", serviceName))
      local ok = pcall(service.Cleanup)
      if not ok then
        warn(string.format("TBRDS: Error during %s service cleanup", serviceName))
      end
    end
    serviceStatus[serviceName] = {
      initialized = false,
      healthy = false,
      lastCheck = os.time(),
    }
  end

  isInitialized = false
  debugLog("TBRDS Service Manager shutdown complete")
end

-- Emergency Recovery
function ServiceManager.EmergencyRecovery(): boolean
  warn("TBRDS: Performing emergency recovery...")

  -- Try to restart all services
  local recoveredServices = 0
  for _, serviceName in ipairs(initializationOrder) do
    local ok = ServiceManager.RestartService(serviceName)
    if ok then
      recoveredServices = recoveredServices + 1
    end
  end
  local success = recoveredServices == #initializationOrder
  if success then
    debugLog("Emergency recovery completed successfully")
  else
    warn(
      string.format(
        "TBRDS: Emergency recovery partially failed - %d/%d services recovered",
        recoveredServices,
        #initializationOrder
      )
    )
  end
  return success
end

-- =============================================================================
-- EXPORTS
-- =============================================================================
return ServiceManager
