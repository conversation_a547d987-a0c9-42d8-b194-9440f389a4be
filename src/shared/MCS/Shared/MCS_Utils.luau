--!strict

--[[
    - file: MCS_Utils.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Modular Command System (MCS) Utilities
      - Reduce code duplication and improve maintainability
      - Provides utility functions for command parsing, debugging, and string manipulation
      - Includes functions for:
        - Debug mode checking
        - Debug printing with module identifier
        - Timer management for performance metrics
        - Command text splitting and sanitization
        - Prefix handling
        - Suggestion filtering
        - Table limiting
		- Table counting
        - Player finding
        - Parse Duration
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Utils: { [string]: any } = {}

-- ============================================================================
-- VARIABLES
-- ============================================================================
local timers: { [string]: number } = {}

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Check if Debug Mode is enabled
function Utils.isDebugMode(): boolean
  return Configuration and Configuration.Settings.DebugMode == true
end

-- Debug print function with module identifier
function Utils.print(moduleName: string, message: string)
  if Utils.isDebugMode() then
    print(string.format("[MCS:%s]: %s", moduleName, message))
  end
end

-- Start Timer
function Utils.startTimer(name: string)
  if Utils.isDebugMode() then
    timers[name] = os.clock()
  end
end

-- End Timer
function Utils.endTimer(moduleName: string, name: string)
  if Utils.isDebugMode() then
    local timer = timers[name]
    if timer then
      local duration = os.clock() - timer
      Utils.print(moduleName, string.format("%s took %.3f ms", name, duration * 1000))
      timers[name] = nil
    end
  end
end

-- Split command text into tokens, handling quoted arguments
function Utils.splitCommandText(text: string): { string }
  local tokens: { string } = {}
  local current: number = 1
  while current <= #text do
    -- Skip whitespace
    local whitespace: string? = text:match("^%s+", current)
    if whitespace then
      current += #whitespace
    end
    if current > #text then
      break
    end

    -- Handle quoted arguments
    if text:sub(current, current) == '"' then
      local endQuote: number? = text:find('"', current + 1, true)
      if endQuote then
        table.insert(tokens, text:sub(current + 1, endQuote - 1))
        current = endQuote + 1
      else
        table.insert(tokens, text:sub(current + 1))
        break
      end
    else
      -- Regular argument
      local nextSpace: number? = text:find("%s", current)
      local nextSpaceIndex = nextSpace or (#text + 1)
      table.insert(tokens, text:sub(current, nextSpaceIndex - 1))
      current = nextSpaceIndex
    end
  end
  return tokens
end

-- Sanitize input to prevent exploits
function Utils.sanitize(text: string): string
  -- Explicitly capture only the first return value
  local result: string = text:gsub("[^%w%s%p]", "")
  return result
end

-- Strips the prefix from a command string if present
function Utils.stripPrefix(text: string, prefix: string): string?
  local pattern = "^%s*" .. prefix .. "(.+)$"

  -- Explicitly capture the first result only
  local result: string? = text:match(pattern)
  return result
end

-- Checks if a string starts with a given prefix
function Utils.hasPrefix(text: string, prefix: string): boolean
  return text:match("^%s*" .. prefix .. "%S+") ~= nil
end

-- Filters a list of suggestions by a partial string (case-insensitive)
function Utils.filterSuggestions(suggestions: { string }, partial: string): { string }
  local results = {}
  local lowerPartial = partial:lower()
  for _, suggestion in ipairs(suggestions) do
    if suggestion:lower():sub(1, #lowerPartial) == lowerPartial then
      table.insert(results, suggestion)
    end
  end
  return results
end

-- Limits a table to a maximum number of elements
function Utils.tableLimit(tbl: { any }, max: number): { any }
  if #tbl > max then
    local limited = table.create(max)
    for i = 1, max do
      limited[i] = tbl[i]
    end
    return limited
  end
  return tbl
end

-- Counts the number of key-value pairs in a table
function Utils.tableCount(tbl: { [any]: any }): number
  local count = 0
  for _ in pairs(tbl) do
    count += 1
  end
  return count
end

-- Finds a player by name, supporting exact and partial matches
-- Returns the player object and an error message if applicable
function Utils.findPlayer(name: string): (Player?, string?)
  if not name or name == "" then
    return nil, "No player name provided"
  end

  local lowerName = name:lower()
  local exactMatch: Player | nil = nil
  local partialMatches: { Player } = {}

  for _, player in ipairs(Players:GetPlayers()) do
    local playerLowerName = player.Name:lower()
    if playerLowerName == lowerName then
      exactMatch = player
      break
    elseif playerLowerName:find(lowerName, 1, true) == 1 then
      table.insert(partialMatches, player)
    end
  end

  if exactMatch then
    return exactMatch, nil
  elseif #partialMatches == 1 then
    return partialMatches[1], nil
  elseif #partialMatches > 1 then
    local names = {}
    for _, p in ipairs(partialMatches) do
      table.insert(names, p.Name)
    end
    return nil, "Multiple players found: " .. table.concat(names, ", ")
  else
    return nil, "Player not found"
  end
end

--[[
Parse a duration string (e.g., "1d" -> 86400 seconds)

@param durationStr The duration string (e.g., "1d", "2h")
@return The duration in seconds, or nil if invalid
]]
function Utils.parseDuration(durationStr: string?): number?
  if not durationStr then
    return nil
  end
  local num = tonumber(durationStr:match("^%d+"))
  if not num then
    return nil
  end
  local unit = durationStr:lower():match("[smhdwmy]")
  if not unit then
    return num -- Default to seconds if no unit
  end
  local multipliers: { [string]: number } = {
    s = 1, -- seconds
    m = 60, -- minutes
    h = 3600, -- hours
    d = 86400, -- days
    w = 604800, -- weeks
    mo = 2592000, -- months (30 days)
    y = 31536000, -- years
  }
  return num * multipliers[unit]
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Utils
