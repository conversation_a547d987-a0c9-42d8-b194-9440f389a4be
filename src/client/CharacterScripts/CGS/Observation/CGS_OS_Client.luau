--!strict

--[[
    - file: CGS_OS_Client.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - description:
      - Core Game System (CGS) Observation Sub-System
      - Features camera effects like:
        - Sway Effect (with idle sway)
        - Bobbing Effect (with movement threshold)
        - Dynamic FOV
        - Underwater Effect
        - Full Body View (Feature)
        - Directional Head (Feature)
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService") :: RunService
local TweenService = game:GetService("TweenService") :: TweenService
local UserInputService = game:GetService("UserInputService") :: UserInputService

-- ============================================================================
-- PATHS
-- ============================================================================
local RpShared = ReplicatedStorage.CGS.Shared
local remotesFolder = ReplicatedStorage.CGS:WaitForChild("Remotes")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration =
  require(ReplicatedStorage.Configurations.Systems:WaitForChild("CGS_Configuration"))
local Types = require(RpShared.Parent:WaitForChild("CGS_Types"))
local Utils = require(RpShared:WaitForChild("CGS_Utils"))

-- ============================================================================
-- REMOTES
-- ============================================================================
local ToggleEffectsRemote = remotesFolder:WaitForChild("ToggleCameraEffects") :: RemoteEvent
local DirectionalHeadRemote = remotesFolder:WaitForChild("DirectionalHeadLook") :: RemoteEvent

-- ============================================================================
-- CONFIGURATION
-- ============================================================================
local TAG = "ObservationSystem_Client_Init" :: string

-- ============================================================================
-- CONSTANTS & STATE
-- ============================================================================
-- Local player and camera references
local localPlayer: Player = Utils.getLocalPlayer()
local camera: Camera? = Utils.getCurrentCamera()

-- Whether camera effects are currently active
local effectsEnabled: boolean = true

-- Dictionary of active effects by name
local activeEffects: { [string]: Types.CameraEffect } = {}

-- Ordered list of effects for deterministic update processing
local sortedEffects: { Types.CameraEffect } = {}

-- ============================================================================
-- FEATURES
-- ============================================================================

-- Body view
local bodyviewFeature: Types.CameraEffect = {
  Enabled = Configuration.Config.Observation.BodyView.Enabled,
  Priority = Configuration.Config.Observation.BodyView.Priority or 1,

  _connection = nil :: RBXScriptConnection?,
  _head = nil :: BasePart?,
  _humanoid = nil :: Humanoid?,

  --[[
		Initializes the body view effect by attaching a render loop that dynamically adjusts
		the camera offset based on raycasting from the head forward.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Init = function(self: Types.CameraEffect)
    local character = localPlayer.Character
    if not character then
      return
    end

    self._humanoid = character:FindFirstChildOfClass("Humanoid")
    self._head = character:FindFirstChild("Head") :: BasePart?
    if not self._humanoid or not self._head then
      return
    end

    self._head.LocalTransparencyModifier = 1
    self._head.Transparency = 0
    self._head.CastShadow = true

    self._humanoid.CameraOffset = Vector3.new(0, 0, -1.35)

    self._connection = RunService.RenderStepped:Connect(function()
      if not character or not self._head or not self._humanoid then
        return
      end

      local origin = self._head.Position
      local direction = self._head.CFrame.LookVector * 2
      local rayParams = RaycastParams.new()
      rayParams.FilterType = Enum.RaycastFilterType.Exclude
      rayParams.FilterDescendantsInstances = { character }

      local result = workspace:Raycast(origin, direction, rayParams)
      if result then
        local hitDistance = (origin - result.Position).Magnitude
        self._humanoid.CameraOffset = Vector3.new(0, 0, -hitDistance)
      else
        self._humanoid.CameraOffset = Vector3.new(0, 0, -1.35)
      end
    end)

    -- Maintain body transparency consistency during rendering
    for _, child in character:GetChildren() do
      if child:IsA("BasePart") and child.Name ~= "Head" then
        child:GetPropertyChangedSignal("LocalTransparencyModifier"):Connect(function()
          child.LocalTransparencyModifier = child.Transparency
        end)
        child.LocalTransparencyModifier = child.Transparency
      end
    end
  end,

  --[[
		Update method for body view (unused but required for interface).

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Update = function() end,

  --[[
		Resets the body view effect by disconnecting the render step and restoring camera offset.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    if self._connection then
      self._connection:Disconnect()
      self._connection = nil
    end
    if self._humanoid then
      self._humanoid.CameraOffset = Vector3.zero
    end
  end,
}

-- Directional Head Movement
local directionalHeadFeature: Types.CameraEffect = {
  Enabled = Configuration.Config.Observation.DirectionalHead.Enabled,
  Priority = Configuration.Config.Observation.DirectionalHead.Priority or 6,

  _character = nil :: Model?,
  _humanoid = nil :: Humanoid?,
  _root = nil :: BasePart?,
  _neck = nil :: Motor6D?,
  _yOffset = 0 :: number,
  _renderConnection = nil :: RBXScriptConnection?,
  _replicationThread = nil :: thread?,

  --[[
		Initializes the directional head effect. Connects to RenderStepped to update
		neck rotation based on the camera direction, and spawns a loop to replicate
		neck rotation to the server.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Init = function(self: Types.CameraEffect)
    local character = localPlayer.Character or localPlayer.CharacterAdded:Wait()
    self._character = character
    self._humanoid = character:FindFirstChildOfClass("Humanoid")
    self._root = character:FindFirstChild("HumanoidRootPart")
    self._neck = character:FindFirstChild("Neck", true) :: Motor6D?
    if not self._neck and character:FindFirstChild("Torso") then
      self._neck = character:WaitForChild("Torso"):FindFirstChild("Neck") :: Motor6D?
    end
    if not (self._humanoid and self._root and self._neck) then
      return
    end

    self._yOffset = self._neck.C0.Y

    -- Continuously update neck C0 every frame
    self._renderConnection = RunService.RenderStepped:Connect(function(dt)
      self:Update(dt, workspace.CurrentCamera :: Camera)
    end)

    -- Periodically replicate the neck's C0 to the server
    self._replicationThread = task.spawn(function()
      while true do
        if self._neck then
          DirectionalHeadRemote:FireServer(self._neck.C0)
        end
        task.wait(1)
      end
    end)
  end,

  --[[
		Updates the neck C0 transformation based on camera's relative direction.

		@param self Types.CameraEffect -- The effect instance.
		@param _ any -- Ignored delta time (passed by RenderStepped).
		@param cam Camera -- The player's current camera.

		@return void
	]]
  Update = function(self: Types.CameraEffect, _: any, cam: Camera)
    if not self.Enabled then
      return
    end
    if not self._neck then
      return
    end
    if not self._root then
      return
    end
    if not cam then
      return
    end
    if not self._humanoid then
      return
    end

    local neck = self._neck :: Motor6D
    local yOffset: number = self._yOffset or 0

    local cameraDirection = self._root.CFrame:ToObjectSpace(cam.CFrame).LookVector
    local yaw = math.asin(cameraDirection.X)
    local pitch = math.asin(cameraDirection.Y)

    -- Clamp neck rotation angles
    yaw = math.clamp(yaw, -math.rad(60), math.rad(60))
    pitch = math.clamp(pitch, -math.rad(30), math.rad(30))

    -- Apply rotation based on rig type
    if self._humanoid.RigType == Enum.HumanoidRigType.R15 then
      neck.C0 = CFrame.new(0, yOffset, 0) * CFrame.Angles(0, -yaw, 0) * CFrame.Angles(pitch, 0, 0)
    else
      neck.C0 = CFrame.new(0, yOffset, 0)
        * CFrame.Angles(3 * math.pi / 2, 0, math.pi)
        * CFrame.Angles(0, 0, -yaw)
        * CFrame.Angles(-pitch, 0, 0)
    end
  end,

  --[[
		Resets the directional head effect: disconnects events, cancels replication,
		and resets the neck's C0 transform.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    if self._renderConnection then
      self._renderConnection:Disconnect()
      self._renderConnection = nil
    end

    local replicationThread = self._replicationThread
    if replicationThread and typeof(replicationThread) == "thread" then
      task.cancel(replicationThread :: thread)
      self._replicationThread = nil
    end

    local yOffset: number = self._yOffset or 0
    if self._neck then
      self._neck.C0 = CFrame.new(0, yOffset, 0)
    end

    self._character = nil
    self._humanoid = nil
    self._root = nil
    self._neck = nil
    self._yOffset = 0
  end,
}

-- ============================================================================
-- CAMERA EFFECTS
-- ============================================================================

-- Bobbing Effect
local bobbingEffect: Types.CameraEffect = {
  Enabled = Configuration.Config.Observation.Bobbing.Enabled,
  Priority = Configuration.Config.Observation.Bobbing.Priority or 2,
  MovementThreshold = Configuration.Config.Observation.Bobbing.MovementThreshold :: number,

  -- Internal state
  _rollTilt = 0 :: number,
  _verticalBop = 0 :: number,
  _horizontalBop = 0 :: number,
  _walkBop = 0 :: number,
  _movementSpeedFactor = 5 :: number,

  --[[
		Update the bobbing effect based on movement and velocity.

		@param self Types.CameraEffect -- The effect instance.
		@param deltaTime number -- Time delta since last frame.
		@param cam Camera -- The player's current camera.

		@return void
	]]
  Update = function(self: Types.CameraEffect, deltaTime: number, cam: Camera)
    if not self.Enabled then
      return
    end

    deltaTime = math.clamp(deltaTime * 30, 0, 1)

    local char = localPlayer.Character
    local humanoid = char and char:FindFirstChildOfClass("Humanoid") :: Humanoid?
    if not humanoid or humanoid.Health <= 0 then
      return
    end

    local root = humanoid.RootPart :: BasePart?
    if not root then
      return
    end

    local velocity = root.AssemblyLinearVelocity
    local horizVel = Vector3.new(velocity.X, 0, velocity.Z)
    local speed = horizVel.Magnitude
    local clampedSpeed = math.min(speed, 25)

    -- Roll tilt from mouse movement
    local mdx = math.clamp(UserInputService:GetMouseDelta().X, -2.5, 2.5)
    self._rollTilt = Utils.lerp(self._rollTilt, mdx, 0.25 * deltaTime)

    -- Vertical bobbing
    self._verticalBop = Utils.lerp(
      self._verticalBop,
      math.sin(tick() * self._movementSpeedFactor) / 5,
      0.25 * deltaTime
    )

    -- Horizontal head tilt based on velocity direction
    local lookX = cam.CFrame:VectorToObjectSpace(velocity / math.max(humanoid.WalkSpeed, 0.01)).X
    self._horizontalBop = Utils.lerp(self._horizontalBop, -lookX * 0.04, 0.1 * deltaTime)
    self._horizontalBop = math.clamp(self._horizontalBop, -0.12, 0.1)

    -- Walk bobbing
    if speed > self.MovementThreshold then
      self._walkBop = Utils.lerp(
        self._walkBop,
        math.cos(tick() * 0.5 * math.floor(self._movementSpeedFactor))
          * (self._movementSpeedFactor / 200),
        0.25 * deltaTime
      )
    else
      self._walkBop = Utils.lerp(self._walkBop, 0, 0.05 * deltaTime)
    end

    -- Adjust movement speed factor for dynamic responsiveness
    if speed > 6 then
      self._movementSpeedFactor = 10
    elseif speed > 0.1 then
      self._movementSpeedFactor = 6
    end

    -- Apply all combined bobbing effects to camera
    cam.CFrame = cam.CFrame
      * CFrame.fromEulerAnglesXYZ(0, 0, math.rad(self._rollTilt))
      * CFrame.fromEulerAnglesXYZ(
        math.rad(self._verticalBop * deltaTime),
        math.rad(self._walkBop * deltaTime),
        self._horizontalBop
      )
      * CFrame.Angles(0, 0, math.rad(self._verticalBop * deltaTime * (clampedSpeed / 5)))
  end,

  --[[
		Reset the bobbing effect's internal state.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    self._rollTilt = 0
    self._verticalBop = 0
    self._horizontalBop = 0
    self._walkBop = 0
    self._movementSpeedFactor = 5
  end,

  --[[
		Initialize the bobbing effect (logging only).

		@return void
	]]
  Init = function()
    Utils.log(TAG, "Bobbing effect initialized")
  end,
}

-- Sway Effect
local swayEffect: Types.CameraEffect = {
  Enabled = Configuration.Config.Observation.Sway.Enabled,
  Priority = Configuration.Config.Observation.Sway.Priority or 3,
  Turn = 0,
  Sensitivity = Configuration.Config.Observation.Sway.TurnSpeed,
  ClampRange = Configuration.Config.Observation.Sway.TurnClamp,
  IdleAmount = Configuration.Config.Observation.Sway.IdleAmount,
  IdleSpeed = Configuration.Config.Observation.Sway.IdleSpeed,

  --[[
		Update the sway effect based on mouse delta and idle animation.

		@param self Types.CameraEffect -- The effect instance.
		@param deltaTime number -- Time delta since last frame.
		@param cam Camera -- The player's current camera.

		@return void
	]]
  Update = function(self: Types.CameraEffect, deltaTime: number, cam: Camera)
    if not self.Enabled then
      return
    end

    -- Apply mouse sway
    local mouseDelta = UserInputService:GetMouseDelta()
    local clampedDelta = math.clamp(mouseDelta.X, -self.ClampRange, self.ClampRange)
    self.Turn = Utils.lerp(self.Turn, clampedDelta, self.Sensitivity * deltaTime)
    local mouseSwayCFrame = CFrame.Angles(0, 0, math.rad(self.Turn))
    cam.CFrame *= mouseSwayCFrame

    -- Apply idle sway when stationary
    local char = localPlayer.Character
    if char then
      local rootPart = char:FindFirstChild("HumanoidRootPart") :: BasePart?
      if rootPart and rootPart.AssemblyLinearVelocity.Magnitude < 0.1 then
        local time = tick()
        local idleAmount = self.IdleAmount :: Vector3
        local pitch = math.sin(time * self.IdleSpeed) * math.rad(idleAmount.X)
        local roll = math.sin(time * self.IdleSpeed * 1.3) * math.rad(idleAmount.Z)
        cam.CFrame *= CFrame.Angles(pitch, 0, roll)
      end
    end
  end,

  --[[
		Reset the sway effect to its default state.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    self.Turn = 0
  end,

  --[[
		Initialize the sway effect (logging only).

		@return void
	]]
  Init = function()
    Utils.log(TAG, "Sway effect with idle sway initialized")
  end,
}

-- Dynamic FOV Effect
local dynamicFovEffect: Types.CameraEffect = {
  Enabled = Configuration.Config.Observation.DynamicFOV.Enabled,
  Priority = Configuration.Config.Observation.DynamicFOV.Priority or 4,

  -- Configuration
  Config = {
    DefaultFOV = Configuration.Config.Observation.DynamicFOV.DefaultFOV,
    SprintFOV = Configuration.Config.Observation.DynamicFOV.SprintFOV,
    JumpFOV = Configuration.Config.Observation.DynamicFOV.JumpFOV,
    FreeFallFOV = Configuration.Config.Observation.DynamicFOV.FreeFallFOV,
    SwimFOV = Configuration.Config.Observation.DynamicFOV.SwimFOV,
    CrouchFOV = Configuration.Config.Observation.DynamicFOV.CrouchFOV,
    ProneFOV = Configuration.Config.Observation.DynamicFOV.ProneFOV,
    TransitionSpeed = Configuration.Config.Observation.DynamicFOV.TransitionSpeed,
  },

  -- State
  connections = {} :: { RBXScriptConnection },
  targetFOV = 70 :: number,
  lastFOV = 70 :: number,
  overrideFOV = nil :: number?,
  currentTween = nil :: Tween?,
  lastUpdateTime = 0 :: number,
  UPDATE_COOLDOWN = 0.1 :: number,

  --[[
		Smoothly tweens the camera's FOV to the specified value.

		@param self Types.CameraEffect -- The effect instance.
		@param fov number -- Target FieldOfView value.
	]]
  tweenFOVTo = function(self: Types.CameraEffect, fov: number)
    if self.currentTween then
      self.currentTween:Cancel()
    end

    local cam: Camera? = workspace.CurrentCamera
    if not cam then
      return
    end

    self.currentTween = TweenService:Create(
      cam,
      TweenInfo.new(0.25, Enum.EasingStyle.Sine, Enum.EasingDirection.Out),
      { FieldOfView = fov }
    )
    self.currentTween:Play()
  end,

  --[[
		Sets or clears an override FOV.

		@param self Types.CameraEffect -- The effect instance.
		@param fov number? -- Override value or nil to reset.
	]]
  SetFOVOverride = function(self: Types.CameraEffect, fov: number?)
    self.overrideFOV = fov
    self:tweenFOVTo(fov or self.targetFOV)
  end,

  --[[
		Gets the current target FOV considering overrides.

		@param self Types.CameraEffect -- The effect instance.
		@return number -- Current FOV.
	]]
  GetTargetFOV = function(self: Types.CameraEffect): number
    return self.overrideFOV or self.targetFOV
  end,

  --[[
		Attempts to retrieve the current Humanoid from the camera subject or character.

		@return Humanoid? -- Found humanoid or nil.
	]]
  getCurrentHumanoid = function(): Humanoid?
    if not camera then
      return nil
    end

    local subject = camera.CameraSubject
    if subject and subject:IsA("Humanoid") then
      return subject
    end

    local char = localPlayer.Character
    return char and char:FindFirstChildOfClass("Humanoid")
  end,

  --[[
		Updates target FOV based on humanoid state
	]]
  updateTargetFOV = function(self: Types.CameraEffect)
    local now = tick()
    if now - self.lastUpdateTime < self.UPDATE_COOLDOWN then
      return
    end
    self.lastUpdateTime = now

    local humanoid = self.getCurrentHumanoid()
    if not humanoid then
      return
    end

    local newFOV = self.Config.DefaultFOV
    local state = humanoid:GetState()

    -- State-based FOV calculation
    if state == Enum.HumanoidStateType.Swimming then
      newFOV = self.Config.SwimFOV
    elseif state == Enum.HumanoidStateType.Freefall then
      newFOV = self.Config.FreeFallFOV
    elseif state == Enum.HumanoidStateType.Jumping then
      newFOV = self.Config.JumpFOV
    elseif humanoid.MoveDirection.Magnitude > 0 then
      if humanoid:GetAttribute("IsSprinting") or humanoid.WalkSpeed > 14 then
        newFOV = self.Config.SprintFOV
      end
    end

    -- Attribute-based overrides
    if humanoid:GetAttribute("IsProned") then
      newFOV = self.Config.ProneFOV
    elseif humanoid:GetAttribute("IsCrouching") then
      newFOV = self.Config.CrouchFOV
    end

    -- Update if changed
    if newFOV ~= self.lastFOV then
      self.targetFOV = newFOV
      self.lastFOV = newFOV

      if not self.overrideFOV then
        self:tweenFOVTo(newFOV)
      end
    end
  end,

  --[[
		Initializes the dynamic FOV system by connecting character/humanoid signals.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Init = function(self: Types.CameraEffect)
    local function onCharacterAdded(character: Model)
      -- Assert the type of self.connections
      local connections = self.connections :: { RBXScriptConnection }

      -- Clear previous connections
      for _, conn in ipairs(connections) do
        conn:Disconnect()
      end
      connections = {} :: { RBXScriptConnection }
      self.connections = connections

      local foundHumanoid = character:FindFirstChildOfClass("Humanoid")
      if not foundHumanoid then
        return
      end
      local humanoid: Humanoid = foundHumanoid :: Humanoid

      -- Connect relevant events
      local stateChangedConn = humanoid.StateChanged:Connect(function()
        self:updateTargetFOV()
      end)
      table.insert(connections, stateChangedConn)

      -- Attribute change connections
      local attributes = { "IsCrouching", "IsProned", "IsSprinting" }
      for _, attr in ipairs(attributes) do
        local attrSignal = humanoid:GetAttributeChangedSignal(attr)
        local attrConn = attrSignal:Connect(function()
          self:updateTargetFOV()
        end)
        table.insert(connections, attrConn)
      end

      -- Humanoid death connection
      local diedConn = humanoid.Died:Connect(function()
        self:SetFOVOverride(nil)
        self.targetFOV = self.Config.DefaultFOV
        self:tweenFOVTo(self.Config.DefaultFOV)
      end)
      table.insert(connections, diedConn)

      -- Initial update
      self:updateTargetFOV()
    end

    -- Set up character tracking
    localPlayer.CharacterAdded:Connect(onCharacterAdded)
    if localPlayer.Character then
      task.defer(onCharacterAdded, localPlayer.Character)
    end

    -- Set initial FOV
    local currentCamera = workspace.CurrentCamera
    if currentCamera then
      currentCamera.FieldOfView = self.Config.DefaultFOV
    end
  end,

  --[[
		Resets the dynamic FOV system by disconnecting all signal connections.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    local conns = self.connections :: { RBXScriptConnection }
    for _, conn in ipairs(conns) do
      conn:Disconnect()
    end
    self.connections = {}
  end,

  --[[
		Dynamic FOV is event-driven; no per-frame update required.
	]]
  Update = function() end,
}

-- Motion Blur Effect
local motionBlurEffect: Types.CameraEffect = {
  Enabled = Configuration.Config.Observation.MotionBlur.Enabled,
  Priority = Configuration.Config.Observation.MotionBlur.Priority or 5,

  _blur = nil :: BlurEffect?,
  _lastLookVector = Vector3.zero,

  --[[
		Initializes the motion blur effect by inserting a BlurEffect into the current camera
		and caching the initial camera look vector.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Init = function(self: Types.CameraEffect)
    local cam: Camera? = workspace.CurrentCamera
    if not cam then
      return
    end

    self._blur = Instance.new("BlurEffect")
    self._blur.Size = 0
    self._blur.Parent = cam

    self._lastLookVector = cam.CFrame.LookVector
  end,

  --[[
		Updates the motion blur effect based on camera rotation and character velocity.

		@param self Types.CameraEffect -- The effect instance.
		@param dt number -- Delta time since last frame.
		@param cam Camera -- The current camera.

		@return void
	]]
  Update = function(self: Types.CameraEffect, dt: number, cam: Camera)
    if not self.Enabled or not self._blur then
      return
    end

    local char = localPlayer.Character
    local hrp = char and char:FindFirstChild("HumanoidRootPart") :: BasePart?
    local humanoid = char and char:FindFirstChildOfClass("Humanoid") :: Humanoid?

    if not (hrp and humanoid) then
      self._blur.Size = math.clamp(
        self._blur.Size - Configuration.Config.Observation.MotionBlur.FadeSpeed * dt,
        0,
        Configuration.Config.Observation.MotionBlur.maxBlur
      )
      return
    end

    local maxBlur = Configuration.Config.Observation.MotionBlur.maxBlur
    local rotSensitivity = Configuration.Config.Observation.MotionBlur.rotationSensitivity
    local speedThreshold = Configuration.Config.Observation.MotionBlur.speedThreshold
    local fadeSpeed = Configuration.Config.Observation.MotionBlur.FadeSpeed

    local currentLookVector = cam.CFrame.LookVector
    local dot = math.clamp(self._lastLookVector:Dot(currentLookVector), -1, 1)
    local angle = math.acos(dot)
    self._lastLookVector = currentLookVector

    local rotBlurRaw = math.deg(angle) * rotSensitivity
    local rotBlur = Utils.smootherStep(rotBlurRaw / maxBlur) * maxBlur

    local speed = hrp.AssemblyLinearVelocity.Magnitude
    local speedBlur = Utils.smootherStep(math.max(speed - speedThreshold, 0) / (maxBlur * 2))
      * maxBlur

    local targetBlur = math.clamp(rotBlur + speedBlur, 0, maxBlur)
    self._blur.Size =
      math.clamp(self._blur.Size + (targetBlur - self._blur.Size) * dt * fadeSpeed, 0, maxBlur)
  end,

  --[[
		Resets the motion blur effect by destroying the BlurEffect instance.

		@param self Types.CameraEffect -- The effect instance.
		@return void
	]]
  Reset = function(self: Types.CameraEffect)
    if self._blur then
      self._blur:Destroy()
      self._blur = nil
    end
  end,
}

-- Underwater Visual Effect
local underwaterEffect: Types.CameraEffect = {
  Enabled = Configuration.Config.Observation.UnderwaterEffect.Enabled,
  Priority = Configuration.Config.Observation.UnderwaterEffect.Priority or 7,

  -- Private state
  _blur = nil :: BlurEffect?,
  _sunRays = nil :: SunRaysEffect?,
  _flash = nil :: ColorCorrectionEffect?,
  _isUnderwater = false,
  _justSurfaced = false,
  _flashTimer = 0,

  --[[
		Initializes all required post-processing effects and attaches them to the camera.
	]]
  Init = function(self: Types.CameraEffect)
    local cam: Camera? = workspace.CurrentCamera
    if not cam then
      return
    end

    self._blur = Instance.new("BlurEffect")
    self._blur.Name = "UnderwaterBlur"
    self._blur.Size = 0
    self._blur.Enabled = false
    self._blur.Parent = cam

    self._sunRays = Instance.new("SunRaysEffect")
    self._sunRays.Name = "UnderwaterSunRays"
    self._sunRays.Intensity = 0
    self._sunRays.Enabled = false
    self._sunRays.Parent = cam

    self._flash = Instance.new("ColorCorrectionEffect")
    self._flash.Name = "SurfaceFlash"
    self._flash.Brightness = 0
    self._flash.Enabled = false
    self._flash.Parent = cam
  end,

  --[[
		Update loop that manages blur/sunrays/flash effects based on character depth.

		@param self Types.CameraEffect -- The effect instance.
		@param dt number -- Delta time since last frame.
		@param _cam Camera -- Current frame camera (unused here).
	]]
  Update = function(self: Types.CameraEffect, dt: number, _cam: Camera)
    local char = localPlayer.Character
    local head = char and char:FindFirstChild("Head") :: BasePart?
    if not head or not self._blur or not self._sunRays or not self._flash then
      return
    end

    local headY = head.Position.Y
    local belowWater = headY < 3

    if belowWater then
      if not self._isUnderwater then
        self._isUnderwater = true
        self._justSurfaced = false
        self._blur.Enabled = true
        self._sunRays.Enabled = true
        self._flash.Enabled = false
        self._flashTimer = 0
      end

      local depth = math.clamp(1 - headY, 0, 30)
      self._blur.Size = 8 + (56 - 8) * (depth / 30)
      self._sunRays.Intensity = 0.5 * math.clamp(1 - (depth / 10), 0, 1)
    else
      if self._isUnderwater then
        self._isUnderwater = false
        self._justSurfaced = true
        self._flashTimer = 0
        self._flash.Enabled = true
        self._flash.Brightness = 0.5
      end

      self._blur.Enabled = false
      self._sunRays.Enabled = false

      if self._justSurfaced then
        self._flashTimer += dt
        if self._flashTimer < 0.4 then
          self._flash.Brightness = 0.5 * (1 - self._flashTimer / 0.4)
        else
          self._flash.Enabled = false
          self._flash.Brightness = 0
          self._justSurfaced = false
        end
      end
    end
  end,

  --[[
		Resets all post-processing effects and internal state flags.
	]]
  Reset = function(self: Types.CameraEffect)
    if self._blur then
      self._blur:Destroy()
      self._blur = nil
    end

    if self._sunRays then
      self._sunRays:Destroy()
      self._sunRays = nil
    end

    if self._flash then
      self._flash:Destroy()
      self._flash = nil
    end

    self._isUnderwater = false
    self._justSurfaced = false
    self._flashTimer = 0
  end,
}

-- ============================================================================
-- EFFECT REGISTRATION
-- ============================================================================
Utils.AddEffect("Bodyview", bodyviewFeature, activeEffects, sortedEffects, TAG)
Utils.AddEffect("Bobbing", bobbingEffect, activeEffects, sortedEffects, TAG)
Utils.AddEffect("Sway", swayEffect, activeEffects, sortedEffects, TAG)
Utils.AddEffect("DynamicFOV", dynamicFovEffect, activeEffects, sortedEffects, TAG)
Utils.AddEffect("MotionBlur", motionBlurEffect, activeEffects, sortedEffects, TAG)
Utils.AddEffect("DirectionalHead", directionalHeadFeature, activeEffects, sortedEffects, TAG)
Utils.AddEffect("Underwater", underwaterEffect, activeEffects, sortedEffects, TAG)

-- ============================================================================
-- CHARACTER EVENTS & MANAGEMENT
-- ============================================================================

--[[
	Called when the local player's character is added to the game.
	Initializes the camera and resets/initializes all active effects.

	@param char Model -- The new character model.
]]
local function onCharacterAdded(char: Model)
  camera = workspace.CurrentCamera
  if camera then
    camera.CameraType = Enum.CameraType.Custom
  end

  -- Log the character addition
  Utils.log(TAG, tostring(char) .. ": character added, resetting camera effects")

  for _, effect in activeEffects do
    if effect.Reset then
      effect:Reset()
    end
    if effect.Init then
      effect:Init()
    end
  end
end

--[[
	Called when the local player's character is removed.
	Resets all active effects.
]]
local function onCharacterRemoving()
  for _, effect in activeEffects do
    if effect.Reset then
      effect:Reset()
    end
  end
end

-- Connect to the CharacterAdded event to initialize camera and effects
localPlayer.CharacterAdded:Connect(onCharacterAdded)

-- Connect to the CharacterRemoving event to reset all active effects
localPlayer.CharacterRemoving:Connect(onCharacterRemoving)

-- If the character already exists, defer initialization
if localPlayer.Character then
  task.defer(onCharacterAdded, localPlayer.Character)
end

-- ============================================================================
-- REMOTE EVENTS
-- ============================================================================

--[[
	Toggles all camera effects based on remote input.

	@param enabled boolean -- Whether effects should be enabled or not.
]]
ToggleEffectsRemote.OnClientEvent:Connect(function(enabled: boolean)
  effectsEnabled = enabled
end)

--[[
	Updates the neck orientation (C0) of a character's head to face a target CFrame.

	@param otherPlayer Player -- The player whose character's neck should be rotated.
	@param targetC0 CFrame -- The target orientation for the neck joint.
]]
DirectionalHeadRemote.OnClientEvent:Connect(function(otherPlayer: Player, targetC0: CFrame)
  if otherPlayer == localPlayer then
    return
  end

  local char = otherPlayer.Character
  local neck = char and char:FindFirstChild("Neck", true) :: Motor6D?
  if neck then
    TweenService
      :Create(neck, TweenInfo.new(0.35, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {
        C0 = targetC0,
      })
      :Play()
  end
end)

-- ============================================================================
-- RENDER LOOP
-- ============================================================================

--[[
	Updates all enabled camera effects each frame.

	@param dt number -- Delta time since last frame.
]]
RunService:BindToRenderStep(
  "CGS_CameraEffects",
  Enum.RenderPriority.Camera.Value + 1,
  function(dt: number)
    if not effectsEnabled then
      return
    end
    camera = workspace.CurrentCamera
    if not camera then
      return
    end

    for _, effect in sortedEffects do
      if effect.Enabled then
        effect:Update(dt, camera)
      end
    end
  end
)
