--!strict

--[[
    - file: CGS_InputHandler.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Unified input handler for CGS Movement Sub-system.
      - Handles keyboard, gamepad, and touch input.
      - Supports movement, sprint, crouch, prone, and jump actions.
      - Provides input state snapshot and debounced jump detection.

    - dependencies:
      - CGS_Types
      - CGS_Configuration
      - CGS_Utils
	  - CGS_Signal
      
    - usage:
      -- Call once on client startup
      InputHandler.Initialize()
      
      -- Each frame or input cycle, call:
      local inputData = InputHandler.GetInputData()
      -- inputData.MoveVector : Vector3 (X left/right, Z forward/back normalized)
      -- inputData.SprintHeld : boolean
      -- inputData.CrouchHeld : boolean
      -- inputData.ProneHeld : boolean
      -- inputData.JumpPressed : boolean (true only on the frame jump was requested)
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players = game:GetService("Players") :: Players
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService") :: UserInputService

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration =
  require(ReplicatedStorage.Configurations.Systems:WaitForChild("CGS_Configuration"))
local Signal = require(ReplicatedStorage.CGS.Movement.CGS_Signal)
local Types = require(ReplicatedStorage.CGS.CGS_Types)
local Utils = require(ReplicatedStorage.CGS.Shared:WaitForChild("CGS_Utils"))

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local InputHandler = {}

-- ============================================================================
-- REFERENCES
-- ============================================================================
local player: Player? = Players.LocalPlayer

local Config = Configuration.Config.Movement

-- ============================================================================
-- DECLARE EVENTS
-- ============================================================================
local OnActionChanged = Signal.new() -- (actionName: string, isPressed: boolean)
local OnJump = Signal.new() -- ()

-- ============================================================================
-- CONSTANTS
-- ============================================================================

-- Tracked Keys Map
local trackedKeys: { [Enum.KeyCode]: Types.KeyName } = {
  [Enum.KeyCode.W] = "W",
  [Enum.KeyCode.A] = "A",
  [Enum.KeyCode.S] = "S",
  [Enum.KeyCode.D] = "D",
  [Enum.KeyCode.LeftShift] = "LeftShift",
  [Enum.KeyCode.LeftControl] = "LeftControl",
  [Enum.KeyCode.C] = "C",
}

-- Movement Direction
local MOVEMENT_DIRECTION: { [string]: Vector3 } = {
  Forward = Vector3.new(0, 0, -1),
  Backward = Vector3.new(0, 0, 1),
  Left = Vector3.new(-1, 0, 0),
  Right = Vector3.new(1, 0, 0),
}

-- Module Name
local TAG = "InputHandler" :: string

-- Jump Cooldown constant
local jumpCooldown: number = Config.JumpCooldown

-- ============================================================================
-- STATES & VARIABLES
-- ============================================================================

-- Keys pressed map
local keysPressed: Types.KeyPressedMap = {
  W = false,
  A = false,
  S = false,
  D = false,
  LeftShift = false,
  LeftControl = false,
  C = false,
}

-- Actions
local boundActions: { [string]: Enum.KeyCode } = {
  Sprint = Enum.KeyCode.LeftShift,
  Crouch = Enum.KeyCode.LeftControl,
  Prone = Enum.KeyCode.C,
}

-- States
local inputEnabled: boolean = true
local gamepadMoveVector: Vector3 = Vector3.zero
local jumpRequested: boolean = false
local lastJumpTime: number = 0
local GAMEPAD_DEADZONE: number = Config.Gamepad_Deadzone

-- ============================================================================
-- PRIVATE METHODS
-- ============================================================================
-- Helper to know if the key is pressed
local function onKeyDown(key: Enum.KeyCode): ()
  local keyName = trackedKeys[key]
  if keyName and not keysPressed[keyName] then
    keysPressed[keyName] = true
    OnActionChanged:Fire(keyName, true)
    Utils.log(TAG, `Key down: {keyName}`)
  end
end

-- Helper to know if the key is NOT pressed
local function onKeyUp(key: Enum.KeyCode): ()
  local keyName = trackedKeys[key]
  if keyName and keysPressed[keyName] then
    keysPressed[keyName] = false
    OnActionChanged:Fire(keyName, false)
    Utils.log(TAG, `Key up: {keyName}`)
  end
end

-- Jump Request
local function onJumpRequest(): ()
  local now = tick()
  if now - lastJumpTime >= jumpCooldown then
    jumpRequested = true
    lastJumpTime = now
    OnJump:Fire()
  end
  Utils.log(TAG, "Jump requested")
end

-- Gamepad Support
local function onGamepadMove(input: InputObject): ()
  local raw = Vector3.new(input.Position.X, 0, -input.Position.Y)

  if raw.Magnitude < GAMEPAD_DEADZONE then
    gamepadMoveVector = Vector3.zero
  else
    gamepadMoveVector = raw
  end
end

-- Caching
local function getKeyNameFromBoundAction(actionName: string): Types.KeyName?
  local keyCode = boundActions[actionName]
  return if keyCode then trackedKeys[keyCode] else nil
end

-- Determine direction enum
local function resolveDirection(vec: Vector3): Types.MovementDirection
  local x, z = vec.X, vec.Z

  if vec.Magnitude == 0 then
    return "None"
  end

  if z < 0 then
    if x < 0 then
      return "ForwardLeft"
    elseif x > 0 then
      return "ForwardRight"
    else
      return "Forward"
    end
  elseif z > 0 then
    if x < 0 then
      return "BackwardLeft"
    elseif x > 0 then
      return "BackwardRight"
    else
      return "Backward"
    end
  else
    if x < 0 then
      return "Left"
    elseif x > 0 then
      return "Right"
    else
      return "None"
    end
  end
end

-- ============================================================================
-- INPUT OVERRIDE
-- ============================================================================

-- Set Input enabled
function InputHandler.SetInputEnabled(enabled: boolean): ()
  inputEnabled = enabled
  Utils.log(TAG, "Input " .. (enabled and "enabled" or "disabled"))
end

-- Check if input is enabled
function InputHandler.IsInputEnabled(): boolean
  return inputEnabled
end

-- ============================================================================
-- PUBLIC METHODS
-- ============================================================================

-- Get Input Data
function InputHandler.GetInputData(): Types.InputData
  -- Combine keyboard + gamepad move input
  local moveVector = Vector3.zero

  if keysPressed.W then
    moveVector += MOVEMENT_DIRECTION.Forward
  end
  if keysPressed.S then
    moveVector += MOVEMENT_DIRECTION.Backward
  end
  if keysPressed.A then
    moveVector += MOVEMENT_DIRECTION.Left
  end
  if keysPressed.D then
    moveVector += MOVEMENT_DIRECTION.Right
  end

  moveVector += gamepadMoveVector

  if moveVector.Magnitude > 1 then
    moveVector = moveVector.Unit
  end

  local outputJump = jumpRequested
  jumpRequested = false

  local sprintKey = getKeyNameFromBoundAction("Sprint")
  local crouchKey = getKeyNameFromBoundAction("Crouch")
  local proneKey = getKeyNameFromBoundAction("Prone")

  local direction = resolveDirection(moveVector)
  Utils.log(TAG, `Direction: {direction}`)

  return {
    MoveVector = moveVector,
    Direction = direction,
    SprintHeld = sprintKey and keysPressed[sprintKey] or false,
    CrouchHeld = crouchKey and keysPressed[crouchKey] or false,
    ProneHeld = proneKey and keysPressed[proneKey] or false,
    JumpPressed = outputJump,
  }
end

-- Bind a custom key
function InputHandler.BindAction(actionName: string, key: Enum.KeyCode): ()
  if boundActions[actionName] then
    boundActions[actionName] = key
    Utils.log(TAG, `Rebound {actionName} -> {key.Name}`)
  else
    Utils.warn(TAG, `Invalid action name: {actionName}`)
  end
end

-- Get Bounded Key
function InputHandler.GetBoundKey(actionName: string): Enum.KeyCode?
  return boundActions[actionName]
end

-- Initialization
function InputHandler.Initialize(): ()
  UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed or not inputEnabled then
      return
    end

    if input.UserInputType == Enum.UserInputType.Keyboard then
      onKeyDown(input.KeyCode)
    end
  end)

  UserInputService.InputEnded:Connect(function(input, gameProcessed)
    if gameProcessed or not inputEnabled then
      return
    end

    if input.UserInputType == Enum.UserInputType.Keyboard then
      onKeyUp(input.KeyCode)
    end
  end)

  UserInputService.JumpRequest:Connect(onJumpRequest)

  UserInputService.InputChanged:Connect(function(input)
    if
      input.UserInputType == Enum.UserInputType.Gamepad1
      and input.KeyCode == Enum.KeyCode.Thumbstick1
    then
      onGamepadMove(input)
    end
  end)
end

-- Reset
function InputHandler.Reset(): ()
  for key in pairs(keysPressed) do
    keysPressed[key] = false
  end
  gamepadMoveVector = Vector3.zero
  jumpRequested = false
  lastJumpTime = 0
end

-- ============================================================================
-- EVENTS
-- ============================================================================

-- Re-export/return events
InputHandler.OnActionChanged = OnActionChanged
InputHandler.OnJump = OnJump

-- ============================================================================
-- EXPORTS
-- ============================================================================
return InputHandler
