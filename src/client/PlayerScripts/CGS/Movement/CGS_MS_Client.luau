--!strict

--[[
    - file: CGS_MS_Client.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Client-side movement initializer

    - dependencies:
      - CGS_Types
      - CGS_Movement_Core
      - CGS_DirectionalMovement
      - CGS_InputHandler
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService: RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local BodyAnimation = require(ReplicatedStorage.CGS.Movement.CGS_DirectionalMovement)
local InputHandler = require(script.Parent:WaitForChild("CGS_InputHandler"))
local MovementCore = require(ReplicatedStorage.CGS.Movement.CGS_Movement_Core)
local Types = require(ReplicatedStorage.CGS.CGS_Types)
local Utils = require(ReplicatedStorage.CGS.Shared:WaitForChild("CGS_Utils"))

-- ============================================================================
-- REMOTE EVENTS
-- ============================================================================
local staminaUpdate = ReplicatedStorage.CGS.Remotes:WaitForChild("staminaUpdate") :: RemoteEvent
local exhaustEvent = ReplicatedStorage.CGS.Remotes:WaitForChild("exhaustEvent") :: RemoteEvent

-- ============================================================================
-- REFERENCES, STATES & CONSTANTS
-- ============================================================================
local player: Player? = Players.LocalPlayer
if not player then
  return
end

local camera: Camera? = workspace.CurrentCamera
if not camera then
  return
end

local movementData: Types.MovementData? = nil
local bodyAnimator: Types.BodyAnimator? = nil
local connection: RBXScriptConnection? = nil
local TAG: string = "MovementSub-System_Client"

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================

-- Update Movement
local function updateMovement(deltaTime: number)
  camera = workspace.CurrentCamera
  if not camera then
    Utils.warn(TAG, "CurrentCamera is nil")
    return
  end

  local character: Model? = player.Character
  if not character then
    Utils.warn(TAG, "Player character is nil")
    return
  end

  local humanoid: Humanoid? = character:FindFirstChildOfClass("Humanoid")
  if not humanoid then
    Utils.warn(TAG, "Humanoid not found")
    return
  end

  if not movementData then
    movementData = MovementCore.CreateMovementData()
  end

  movementData = MovementCore.UpdateMovement(
    movementData,
    InputHandler.GetInputData(),
    character,
    camera.CFrame,
    deltaTime
  )
  if not movementData then
    Utils.warn(TAG, "UpdateMovement returned nil")
    return
  end

  local stateSpeed = MovementCore.GetStateSpeed(movementData.State)
  humanoid.WalkSpeed = stateSpeed

  if movementData.Velocity.Magnitude > 0.1 then
    humanoid:Move(movementData.Velocity.Unit, true)
  end

  if bodyAnimator then
    BodyAnimation.UpdateAnimation(bodyAnimator, movementData.InputDirection, movementData.State)
    BodyAnimation.UpdateJoints(bodyAnimator)
  end
end

-- On Character Added
local function onCharacterAdded(character: Model)
  local humanoid: Humanoid = character:WaitForChild("Humanoid") :: Humanoid
  Utils.log(TAG, "Humanoid: " .. humanoid.Name .. " added")

  movementData = MovementCore.CreateMovementData()
  bodyAnimator = BodyAnimation.CreateAnimator(character)

  if connection then
    connection:Disconnect()
  end
  connection = RunService.Heartbeat:Connect(updateMovement)
end

-- On Character Leave/Reset
local function onCharacterRemoving()
  if connection then
    connection:Disconnect()
    connection = nil
  end
  bodyAnimator = nil
end

-- ============================================================================
-- INITIALIZATION & PLAYER CHECK
-- ============================================================================

-- Initialize input handler explicitly
InputHandler.Initialize()

-- Setup character if exists at start
if player.Character then
  onCharacterAdded(player.Character)
end

-- ============================================================================
-- EVENTS
-- ============================================================================
player.CharacterAdded:Connect(onCharacterAdded)
player.CharacterRemoving:Connect(onCharacterRemoving)

-- Handle jump event from InputHandler for animations or sounds (currently just here for logging)
InputHandler.OnJump:Connect(function()
  Utils.log(TAG, "Jump event received")
end)

-- Stamina
staminaUpdate.OnClientEvent:Connect(function(newStamina)
  if movementData then
    movementData.Stamina = newStamina
  end
end)

-- Stamina Exhaustion
exhaustEvent.OnClientEvent:Connect(function(isExhausted)
  if isExhausted then
    -- Camera effect
    Utils.log(TAG, "Exhaust effect started")
  else
    -- Remove camera effect
    Utils.log(TAG, "Exhaust effect ended")
  end
end)
