--!strict

--[[
  - file: TBRDS_Client_Initialization

  - version: 2.0.0
  - author: BleckWolf25
  - contributors:

  - description:
    - Client-side initialization of TBRDS
    - No client-side control, everything is handled server-side
]]

-- =============================================================================
-- SERVICES
-- =============================================================================

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- =============================================================================
-- PLAYER REFERENCE
-- =============================================================================

local LocalPlayer = Players.LocalPlayer

-- =============================================================================
-- MODULES
-- =============================================================================

local Types = require(ReplicatedStorage.TBRDS.TBRDS_Types)
local Utils = require(ReplicatedStorage.TBRDS.Shared.TBRDS_Utils)
local TBRDSConstants = Types.TBRDSConstants

-- =============================================================================
-- TYPE ANNOTATIONS & DEFINITIONS
-- =============================================================================

type RoleName = Types.RoleName -- This is not a error
type RoleStyle = Types.RoleStyle -- This is not a error

-- =============================================================================
-- REMOTE REFERENCES
-- =============================================================================

local TBRDS = ReplicatedStorage:WaitForChild("TBRDS")
local Remotes = TBRDS:WaitForChild("Remotes")
local TagRemote = Remotes:WaitForChild(TBRDSConstants.REMOTE_NAMES.TagUpdate)
local TagRequestRemote = Remotes:WaitForChild("TagRequestRemote")

-- Type guards for remotes
local tagRemoteEvent = if TagRemote:IsA("RemoteEvent") then TagRemote else nil
local tagRequestRemoteEvent = if TagRequestRemote:IsA("RemoteEvent") then TagRequestRemote else nil

-- =============================================================================
-- DEBUG LOGGING
-- =============================================================================

-- Debug logging
local function debugLog(message: string)
  Utils.print("[Client]", message)
end

-- =============================================================================
-- EVENT HANDLERS
-- =============================================================================

-- Handle tag updates from server
if tagRemoteEvent then
  tagRemoteEvent.OnClientEvent:Connect(function(player: Player, tag: RoleName, _style: RoleStyle)
    if player == LocalPlayer then
      -- This is handled by the server
      return
    end

    -- This is now just for verification/debugging
    debugLog(string.format("Received tag update for %s: %s", player.Name, tag))
  end)
end

-- Request our tag when character loads
if LocalPlayer then
  LocalPlayer.CharacterAdded:Connect(function()
    if tagRequestRemoteEvent then
      tagRequestRemoteEvent:FireServer()
      debugLog("Requested tag from server")
    end
  end)
end

-- Request tag when we join
if LocalPlayer and LocalPlayer.Character then
  if tagRequestRemoteEvent then
    tagRequestRemoteEvent:FireServer()
    debugLog("Requested tag from server (initial)")
  end
end
