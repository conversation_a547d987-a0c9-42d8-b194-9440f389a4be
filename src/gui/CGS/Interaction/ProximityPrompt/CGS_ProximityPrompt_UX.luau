--!strict

--[[
    - file: CGS_ProximityPrompt_UX.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors: Silver

    - copyright: Dynamic Innovative Studio

    - description:
      - Custom Proximity Prompt UX system for enhanced user experience with interactions
      - Provides animated progress bars and custom styling for ProximityPrompts
      - Part of the CGS (Core Game System) Interaction sub-system
      - Automatically manages tagged ProximityPrompts with "CustomPrompt" collection tag

    - requirements:
      - ProximityPrompt.Style must be set to Custom
      - ProximityPrompt must have "CustomPrompt" tag
      - BillboardGui hierarchy: BillboardGui > ActionText, KeyText > Progress

    - features:
      - Animated progress bar during hold interactions
      - Dynamic key binding display
      - Automatic prompt state management
      - Memory-efficient prompt tracking
      - Graceful cleanup on prompt removal
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local CollectionService = game:GetService("CollectionService")
local TweenService = game:GetService("TweenService")

-- ============================================================================
-- UI REFERENCES
-- ============================================================================
-- Main BillboardGui container for the proximity prompt UI
local BillboardGui = script.Parent

-- Text label displaying the action description (e.g., "Press to interact")
local ActionText = BillboardGui:WaitForChild("ActionText")

-- Text label showing the key binding (e.g., "E", "F", etc.)
local KeyText = BillboardGui:WaitForChild("KeyText")

-- Progress bar frame that fills during hold interactions
local Progress = KeyText:WaitForChild("Progress")

-- ============================================================================
-- CONSTANTS
-- ============================================================================
-- Tween configuration for resetting progress bar animations
local TWEEN_INFO_RESET = TweenInfo.new(0.15, Enum.EasingStyle.Quad, Enum.EasingDirection.In)

-- Tracks which ProximityPrompts are already connected to prevent duplicate connections
local connectedPrompts: { [Instance]: boolean } = {}

-- ============================================================================
-- STATE
-- ============================================================================
-- Reference to the currently active tween animation
local CurrentTween: Tween? = nil

-- ============================================================================
-- PROGRESS ANIMATION SYSTEM
-- ============================================================================

--[[
    Animates the progress bar for hold-type proximity prompts

    @param show: boolean - Whether to show (true) or hide (false) the progress animation
    @param duration: number? - Optional duration for the fill animation (defaults to 1 second)

    Behavior:
    - When showing: Creates a left-to-right fill animation over the specified duration
    - When hiding: Quickly resets the progress bar and hides it
    - Automatically cancels any existing animation before starting new ones
]]
local function AnimateProgressPrompt(show: boolean, duration: number?)
  -- Cancel any currently running animation to prevent conflicts
  if CurrentTween then
    CurrentTween:Cancel()
    CurrentTween = nil
  end

  if show then
    -- Initialize progress bar for fill animation
    Progress.Size = UDim2.fromScale(0, 1)
    Progress.Visible = true
    Progress.BackgroundTransparency = 0.75

    -- Create smooth fill animation from 0% to 100% width
    local fillTween = TweenService:Create(
      Progress,
      TweenInfo.new(duration or 1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
      {
        -- Fill to 100% width
        Size = UDim2.fromScale(1, 1),
      }
    )
    CurrentTween = fillTween

    -- Start the fill animation
    if CurrentTween then
      CurrentTween:Play()
    end
  else
    -- Create quick reset animation back to 0% width
    local resetTween = TweenService:Create(Progress, TWEEN_INFO_RESET, {
      Size = UDim2.fromScale(0, 1),
    })
    CurrentTween = resetTween

    -- Start the reset animation
    if CurrentTween then
      CurrentTween:Play()
    end

    -- Hide progress bar completely after reset animation completes
    resetTween.Completed:Once(function()
      Progress.Visible = false
      Progress.BackgroundTransparency = 1
    end)
  end
end

-- ============================================================================
-- PROXIMITY PROMPT SETUP SYSTEM
-- ============================================================================

--[[
    Sets up event connections and UI management for a ProximityPrompt

    @param prompt: ProximityPrompt - The proximity prompt instance to configure

    Requirements:
    - prompt.Style must be set to Custom (not Default)
    - prompt must not already be connected (prevents duplicate connections)

    Functionality:
    - Connects to all relevant ProximityPrompt events
    - Manages BillboardGui visibility and content
    - Handles progress animations for hold-type prompts
    - Automatically cleans up when prompt is removed from game
]]
local function SetProximityPrompt(prompt: ProximityPrompt)
  -- Skip if prompt uses default style or is already connected
  if prompt.Style == Enum.ProximityPromptStyle.Default or connectedPrompts[prompt] then
    return
  end

  -- Mark this prompt as connected to prevent duplicate setup
  connectedPrompts[prompt] = true

  -- Clean up tracking when prompt is removed from the game
  prompt.AncestryChanged:Connect(function(_, parent)
    if not parent then
      connectedPrompts[prompt] = nil
    end
  end)

  -- Handle prompt visibility when shown to player
  prompt.PromptShown:Connect(function()
    -- Avoid duplicate UI setup if already showing for this prompt
    if BillboardGui.Enabled and BillboardGui.Adornee == prompt.Parent then
      return
    end

    -- Configure BillboardGui to display over the prompt's parent object
    BillboardGui.Adornee = prompt.Parent
    BillboardGui.Enabled = true

    -- Ensure UI renders above other objects
    BillboardGui.AlwaysOnTop = true

    -- Update UI text with prompt's configuration
    ActionText.Text = prompt.ActionText

    -- Display the appropriate key binding
    local keyName = prompt.KeyboardKeyCode.Name
    if keyName == "Unknown" then
      KeyText.Text = "..." -- Fallback for unrecognized keys
    else
      KeyText.Text = keyName
    end
  end)

  -- Handle prompt hiding when player moves away or prompt is disabled
  prompt.PromptHidden:Connect(function()
    BillboardGui.Enabled = false
    BillboardGui.Adornee = nil

    -- Ensure any active progress animation is stopped
    AnimateProgressPrompt(false)
  end)

  -- Handle start of hold interaction (player presses and holds key)
  prompt.PromptButtonHoldBegan:Connect(function()
    -- Start progress animation with the prompt's configured hold duration
    AnimateProgressPrompt(true, prompt.HoldDuration)
  end)

  -- Handle cancellation of hold interaction (player releases key early)
  prompt.PromptButtonHoldEnded:Connect(function()
    -- Reset progress animation immediately
    AnimateProgressPrompt(false)
  end)
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

--[[
    Type guard function to verify if an instance is a ProximityPrompt

    @param inst: Instance - The instance to check
    @return boolean - True if the instance is a ProximityPrompt, false otherwise

    Used for type safety when processing CollectionService tagged objects
]]
local function isProximityPrompt(inst: Instance): boolean
  return inst:IsA("ProximityPrompt")
end

-- ============================================================================
-- INITIALIZATION SYSTEM
-- ============================================================================

--[[
    Process all existing ProximityPrompts that already have the "CustomPrompt" tag

    This ensures prompts tagged before this script runs are still handled
]]
for _, prompt in ipairs(CollectionService:GetTagged("CustomPrompt")) do
  if isProximityPrompt(prompt) then
    -- Use task.defer to prevent blocking the main thread during initialization
    task.defer(SetProximityPrompt, prompt)
  end
end

-- ============================================================================
-- RUNTIME EVENT HANDLING
-- ============================================================================

--[[
    Handle newly tagged ProximityPrompts during runtime
    This catches prompts that receive the "CustomPrompt" tag after script initialization

    NOTE: We do NOT use RunService or Workspace:GetDescendants because of performance
]]
CollectionService:GetInstanceAddedSignal("CustomPrompt"):Connect(function(instance)
  if isProximityPrompt(instance) then
    -- Type assertion (since we've verified it's a ProximityPrompt)
    SetProximityPrompt(instance :: ProximityPrompt)
  end
end)
