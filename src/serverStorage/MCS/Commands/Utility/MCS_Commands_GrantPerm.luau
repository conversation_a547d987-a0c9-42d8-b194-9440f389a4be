--!strict

--[[
    - file: MCS_Commands_GrantPerm.luau
    
    - version: 1.0.0
    - author: BleckWolf25
    - contributors:
    
    - copyright: Dynamic Innovative Studio
    
    - description:
      - Command to grant a permission level to a player in a private server
      - Only usable by the private server owner
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")

-- ============================================================================
-- MODULES
-- ============================================================================
local PermissionService =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PermissionService"))
local PrivateServerPermissions =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PrivateServerPermissions"))
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local GrantPermCommand = {}

-- ============================================================================
-- COMMAND METADATA
-- ============================================================================
GrantPermCommand.Aliases = { "grantperm" } :: { string }
GrantPermCommand.Description = "Grant a permission level to a player in this private server."
GrantPermCommand.Usage = "grantperm <player> <level>"
GrantPermCommand.PermissionLevel = PermissionService.Levels.SeniorModerators

-- ============================================================================
-- EXECUTE FUNCTION
-- ============================================================================
function GrantPermCommand.Execute(
  player: Player,
  args: { string },
  prefix: string
): Types.CommandResponse
  -- Check if in a private server
  if game.PrivateServerId == "" then
    return { success = false, message = "This command can only be used in private servers." }
  end

  -- Check if player is the private server owner
  if player.UserId ~= game.PrivateServerOwnerId then
    return { success = false, message = "Only the private server owner can use this command." }
  end

  -- Validate arguments
  if #args < 2 then
    return { success = false, message = "Usage: " .. prefix .. GrantPermCommand.Usage }
  end

  -- Find target player
  local targetPlayer: Player? = Utils.findPlayer(args[1])
  if not targetPlayer then
    return { success = false, message = "Player not found: " .. args[1] }
  end

  -- Validate permission level
  local level: number? = tonumber(args[2])
  if not level or level < 1 or level > PermissionService.Levels.SeniorModerators then
    return {
      success = false,
      message = "Invalid level. Use 1 to " .. tostring(PermissionService.Levels.SeniorModerators),
    }
  end

  -- Set permission
  PrivateServerPermissions.SetPlayerPermission(game.PrivateServerId, targetPlayer.UserId, level)
  return {
    success = true,
    message = "Granted level " .. tostring(level) .. " to " .. targetPlayer.Name,
  }
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return GrantPermCommand
