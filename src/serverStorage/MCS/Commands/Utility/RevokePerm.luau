--!strict

--[[
    - file: RevokePerm.luau
    
    - version: 1.0.0
    - author: BleckWolf25
    - contributors
    
    - copyright: Dynamic Innovative Studio
    
    - description:
      - Command to revoke a custom permission from a player in a private server
      - Only usable by the private server owner
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")

-- ============================================================================
-- MODULES
-- ============================================================================
local PermissionService =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PermissionService"))
local PrivateServerPermissions =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PrivateServerPermissions"))
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local RevokePermCommand = {}

-- ============================================================================
-- COMMAND METADATA
-- ============================================================================
RevokePermCommand.Aliases = { "revokeperm" } :: { string }
RevokePermCommand.Description = "Revoke custom permission from a player in this private server."
RevokePermCommand.Usage = "revokeperm <player>" -- Usage without prefix
RevokePermCommand.PermissionLevel = PermissionService.Levels.SeniorModerators

-- ============================================================================
-- EXECUTE FUNCTION
-- ============================================================================
function RevokePermCommand.Execute(
  player: Player,
  args: { string },
  prefix: string
): Types.CommandResponse
  -- Check if in a private server
  if game.PrivateServerId == "" then
    return { success = false, message = "This command can only be used in private servers." }
  end

  -- Check if player is the private server owner
  if player.UserId ~= game.PrivateServerOwnerId then
    return { success = false, message = "Only the private server owner can use this command." }
  end

  -- Validate arguments
  if #args < 1 then
    return { success = false, message = "Usage: " .. prefix .. RevokePermCommand.Usage }
  end

  -- Find target player
  local targetPlayer: Player? = Utils.findPlayer(args[1])
  if not targetPlayer then
    return { success = false, message = "Player not found: " .. args[1] }
  end

  -- Remove permission
  PrivateServerPermissions.RemovePlayerPermission(game.PrivateServerId, targetPlayer.UserId)
  return { success = true, message = "Revoked permission from " .. targetPlayer.Name }
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return RevokePermCommand
