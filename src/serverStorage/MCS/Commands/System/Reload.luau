--!strict

--[[
    - file: Reload.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
	  - Hot-reload all commands (including system)
      - Useful for development, testing and debugging.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")
local ServerStorage = game:GetService("ServerStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local CommandDispatcher =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_CommandDispatcher"))
local Middleware =
  require(ServerScriptService.MCS.Core.Middleware:WaitForChild("MCS_Middleware_Initialization"))
local PermissionService =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PermissionService"))
local Remotes = require(ServerScriptService.MCS.Core:WaitForChild("MCS_Remotes"))
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local ReloadCommand = {}

-- ============================================================================
-- COMMAND METADATA
-- ============================================================================
ReloadCommand.Aliases = { "reload" }
ReloadCommand.Description = "Hot-reloads all commands and systems"
ReloadCommand.Usage = "reload"
ReloadCommand.PermissionLevel = PermissionService.Levels.Anonmancer

-- ============================================================================
-- EXECUTE FUNCTION
-- ============================================================================
function ReloadCommand.Execute(
  player: Player,
  args: { string },
  prefix: string
): Types.CommandResponse
  Utils.print("ReloadCommand", "Starting system reload...")

  -- 1. Reload command system
  local success: boolean, message: string? = pcall(function()
    CommandDispatcher.reload()
  end)

  if not success then
    Utils.print(
      "ReloadCommand",
      "CommandDispatcher reload failed: " .. tostring(message or "unknown error")
    )
    return { success = false, message = "Command reload failed" }
  end

  -- 2. Reload middleware
  success, message = pcall(function()
    Middleware.reload()
  end)

  if not success then
    Utils.print("ReloadCommand", "Middleware reload failed: " .. tostring(message))
    return { success = false, message = "Middleware reload failed" }
  end

  -- 3. Reload remotes
  success, message = pcall(function()
    Remotes.cleanup()
    Remotes.init()
  end)

  if not success then
    Utils.print("ReloadCommand", "Remotes reload failed: " .. tostring(message))
    return { success = false, message = "Remotes reload failed" }
  end

  -- 4. Re-register commands (including this one)
  success, message = pcall(function()
    local commandsFolder = ServerStorage:FindFirstChild("MCS"):FindFirstChild("Commands")
    if commandsFolder then
      for _, module in ipairs(commandsFolder:GetDescendants()) do
        if module:IsA("ModuleScript") then
          require(module)
        end
      end
    end
  end)

  if not success then
    Utils.print("ReloadCommand", "Command re-registration failed: " .. tostring(message))
    return { success = false, message = "Command registration failed" }
  end

  Utils.print("ReloadCommand", "System reload completed successfully")
  return { success = true, message = "System reloaded successfully" }
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return ReloadCommand
