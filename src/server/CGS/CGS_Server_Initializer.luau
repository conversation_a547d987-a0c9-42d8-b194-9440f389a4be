--!strict

--[[
    - file: CGS_Server_Initializer.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Initializes all the sub-systems of Core Game System (CGS).
      - Loads and starts all required modules for movement, observation, inventory, remotes, anti-exploit, and event handling.
      - Handles player character tracking and cinematic zone events.

    - dependencies:
      - CGS_AnimationHandler
      - CGS_AntiExploitService
      - CGS_Remotes
      - CGS_Configuration

    - usage:
      - This script should be the main server entry point to initialize all CGS sub-systems.

    - notes:
      - Designed for use by server-side systems.
      - Extend or adjust logic to add new sub-systems or event handlers as needed.
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService: RunService = game:GetService("RunService")
local ServerScriptService: ServerScriptService = game:GetService("ServerScriptService")

-- ============================================================================
-- PATHS
-- ============================================================================
local cgsPath = ReplicatedStorage:WaitForChild("CGS")
local movementPath = script.Parent:WaitForChild("Movement")
local noExploitsPath = script.Parent:WaitForChild("NoExploits")
local zonesFolder = workspace
  :WaitForChild("CGS-TESTING")
  :WaitForChild("Observation")
  :WaitForChild("CinematicZonesTesting")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage:WaitForChild("Configurations")
    :WaitForChild("Systems")
    :WaitForChild("CGS_Configuration")
)
local Remotes = require(cgsPath:WaitForChild("CGS_Remotes"))
local Utils = require(cgsPath:WaitForChild("Shared"):WaitForChild("CGS_Utils"))

-- ============================================================================
-- INITIALIZATION
-- ============================================================================
local TAG: string = "Server_Initializer"
Utils.log(TAG, "Server initializer started")

-- Initialize Remotes BEFORE accessing them
Utils.log(TAG, "About to initialize remotes")
Remotes.init()
Utils.log(TAG, "Remotes initialized")

-- ============================================================================
-- REMOTES
-- ============================================================================
local remotesPath = cgsPath:WaitForChild("Remotes")

local CinematicEffRemote = remotesPath:WaitForChild("TriggerCinematicEffect") :: RemoteEvent
local DirectionalHeadRemote = remotesPath:WaitForChild("DirectionalHeadLook") :: RemoteEvent
local staminaUpdate = remotesPath:FindFirstChild("staminaUpdate") :: RemoteEvent
local exhaustEvent = remotesPath:FindFirstChild("exhaustEvent") :: RemoteEvent
local jumpRequest = remotesPath:FindFirstChild("jumpRequest") :: RemoteEvent

-- ============================================================================
-- SUB-SYSTEMS MODULES
-- ============================================================================
local NoExploitsSystem =
  require(noExploitsPath:WaitForChild("Security"):WaitForChild("CGS_AntiExploitService"))
local StaminaManager = require(movementPath:WaitForChild("CGS_StaminaManager"))

-- ============================================================================
-- STATES
-- ============================================================================
local partToEffectMap: { [string]: string } = {
  ["DramaticZone"] = "Dramatic",
  ["ExplosionZone"] = "Explosion",
  ["EarthquakeZone"] = "Earthquake",
  ["ImpactZone"] = "Impact",
  ["SubtleZone"] = "Subtle",
}

local playerCharacters: { [Player]: Model? } = {}

-- ============================================================================
-- FUNCTIONS
-- ============================================================================
local function trackPlayerCharacters()
  Players.PlayerAdded:Connect(function(player)
    player.CharacterAdded:Connect(function(character)
      playerCharacters[player] = character
    end)
    player.CharacterRemoving:Connect(function()
      playerCharacters[player] = nil
    end)
  end)

  Players.PlayerRemoving:Connect(function(player)
    playerCharacters[player] = nil
  end)

  for _, player in ipairs(Players:GetPlayers()) do
    if player.Character then
      playerCharacters[player] = player.Character
    end
  end
end

local function initializeCore()
  Utils.log(TAG, "Initializing Core")
  Utils.log(TAG, "Finished initializing the Core")
end

local function initializeAllSubSystems()
  StaminaManager.Initialize()
  NoExploitsSystem.Start()
end

-- ============================================================================
-- CINEMATIC ZONE EVENTS
-- ============================================================================
for _, part in pairs(zonesFolder:GetChildren()) do
  if not part:IsA("BasePart") then
    continue
  end

  part.CanCollide = true
  part.CanTouch = true

  part.Touched:Connect(function(hit)
    local character = hit:FindFirstAncestorOfClass("Model")
    local player = character and Players:GetPlayerFromCharacter(character)
    if not player then
      return
    end

    local effectName = partToEffectMap[part.Name]
    if effectName then
      CinematicEffRemote:FireClient(player, effectName)
      Utils.log(TAG, `Cinematic Effect Triggered: {effectName} for player: {player.Name}`)
    else
      Utils.log(TAG, `No cinematic effect mapped for part: {part.Name}`, "Warn")
    end
  end)
end

-- ============================================================================
-- EVENTS
-- ============================================================================
DirectionalHeadRemote.OnServerEvent:Connect(function(sender: Player, neckCFrame: CFrame)
  local senderCharacter = playerCharacters[sender]
  if not senderCharacter then
    return
  end

  local senderHead = senderCharacter:FindFirstChild("Head") :: BasePart?
  if not senderHead then
    return
  end

  local activationDistance = Configuration.Config.Observation.DirectionalHead.ActivationDistance

  for otherPlayer, otherCharacter in pairs(playerCharacters) do
    if otherPlayer == sender or not otherCharacter then
      continue
    end

    local otherHead = otherCharacter:FindFirstChild("Head") :: BasePart?
    if not otherHead then
      continue
    end

    if (otherHead.Position - senderHead.Position).Magnitude <= activationDistance then
      DirectionalHeadRemote:FireClient(otherPlayer, sender, neckCFrame)
    end
  end
end)

-- ============================================================================
-- MAIN EXECUTION
-- ============================================================================
initializeCore()
initializeAllSubSystems()
trackPlayerCharacters()

RunService.Heartbeat:Connect(function(deltaTime)
  StaminaManager.Update(deltaTime)
end)

Utils.log(TAG, "Server initializer completed")
