--!strict

--[[
    - file: TBRDS_Server.luau

    - version: 2.1.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
        - The TBRDS Server script initializes the TBRDS system, sets up services, handles player roles, and integrates with other systems.
        - It includes error handling, performance monitoring, and admin commands for system management.
        - The script is designed to be robust, with automatic recovery mechanisms and detailed logging for debugging purposes.
]]

-- =============================================================================
-- SERVICES
-- =============================================================================
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerStorage = game:GetService("ServerStorage")

-- =============================================================================
-- MODULES
-- =============================================================================
local TBRDSRemotes = require(ReplicatedStorage.TBRDS:WaitForChild("TBRDS_Remotes"))
local Types = require(ReplicatedStorage.TBRDS:WaitForChild("TBRDS_Types"))

-- Load EventSystem with error handling
local EventSystem
do
  local success, module = pcall(function()
    return require(ReplicatedStorage.TBRDS.Shared:WaitForChild("TBRDS_EventSystem"))
  end)
  if not success then
    warn("TBRDS: Failed to load TBRDS_EventSystem: " .. tostring(module))
    return -- Abort script execution
  end
  EventSystem = module
end

-- Load PerformanceMonitor with error handling
local PerformanceMonitor
do
  local success, module = pcall(function()
    return require(ReplicatedStorage.TBRDS.Shared:WaitForChild("TBRDS_PerformanceMonitor"))
  end)
  if not success then
    warn("TBRDS: Failed to load TBRDS_PerformanceMonitor: " .. tostring(module))
    return
  end
  PerformanceMonitor = module
end

-- Load ServiceManager with error handling
local ServiceManager: Types.ServiceManager -- Redefine as non-optional after initialization
do
  local servicesFolder = ReplicatedStorage.TBRDS:FindFirstChild("Services")
  if not servicesFolder then
    warn("TBRDS: Services folder not found in ReplicatedStorage.TBRDS")
    return
  end
  local success, module = pcall(function()
    return require(servicesFolder:WaitForChild("TBRDS_ServiceManager"))
  end)
  if not success then
    warn("TBRDS: Failed to load TBRDS_ServiceManager: " .. tostring(module))
    return
  end
  ServiceManager = module :: Types.ServiceManager -- Assert non-nil after successful load
end

-- =============================================================================
-- TYPE ANNOTATIONS & DEFINITIONS
-- =============================================================================
type TBRDSSystem = {
  ServiceManager: Types.ServiceManager,
  GetPlayerRole: (Player) -> string,
  RefreshPlayerTag: (Player) -> boolean,
  GetSystemHealth: () -> string,
  IsSystemHealthy: () -> boolean,
}

-- =============================================================================
-- MODULE STATES
-- =============================================================================
local isSystemRunning: boolean = false
local startupTime: number = 0

-- =============================================================================
-- HELPER FUNCTIONS
-- =============================================================================
--[[
    Logs a debug message with the TBRDS prefix and timestamp.
    @param message string -- The message to log.
]]
local function debugLog(message: string): ()
  local timestamp: string = DateTime.now():ToIsoDate()
  print(string.format("[TBRDS:Server][%s]: %s", timestamp, message))
end

-- =============================================================================
-- INITIALIZATION
-- =============================================================================
--[[
    Initializes the TBRDS system by loading services and remote events.
    @return boolean -- True if initialization succeeds, false otherwise.
]]
local function initializeSystem(): boolean
  debugLog("Starting TBRDS system initialization...")
  startupTime = tick()

  -- Initialize remote events first to prevent infinite yields
  if not TBRDSRemotes.InitializeRemotes() then
    warn("TBRDS: Failed to initialize remote events")
    return false
  end

  -- Initialize the service manager and all services
  if not ServiceManager.Initialize() then
    warn("TBRDS: Failed to initialize service manager")
    return false
  end

  -- Verify all services are healthy
  if not ServiceManager.AreAllServicesHealthy() then
    warn("TBRDS: Not all services are healthy after initialization")
    return false
  end

  local initTime: number = (tick() - startupTime) * 1000
  debugLog(string.format("TBRDS system initialized successfully in %.2fms", initTime))
  return true
end

-- =============================================================================
-- SYSTEM MONITORING
-- =============================================================================
--[[
    Sets up system monitoring, including admin commands via chat.
]]
local function setupSystemMonitoring(): ()
  -- Admin command handler
  local function handleAdminCommands(player: Player, message: string): ()
    local configService = ServiceManager.GetService("Configuration") :: Types.ConfigurationService?
    if not configService then
      return
    end

    -- Check admin permissions (rank 252 or above)
    local groupId: number = configService.GetGroups().Primary.Id
    local success, rank = pcall(function()
      return player:GetRankInGroup(groupId)
    end)
    if not success or rank < 252 then
      return
    end

    local command: string = string.lower(message)
    if command == "/tbrds status" then
      local report: string = ServiceManager.GetSystemHealthReport()
      print("=== TBRDS Status Report ===")
      print(report)
    elseif command == "/tbrds restart" then
      debugLog(string.format("System restart requested by %s", player.Name))
      if ServiceManager.EmergencyRecovery() then
        debugLog("System restart completed successfully")
      else
        warn("System restart failed")
      end
    elseif command == "/tbrds metrics" then
      if not PerformanceMonitor then
        warn("TBRDS: PerformanceMonitor is nil")
        return
      end
      local metrics = PerformanceMonitor.GetDetailedMetrics() :: Types.PerformanceMetrics
      print("=== TBRDS Performance Metrics ===")
      print(string.format("Uptime: %d seconds", metrics.uptime))
      print(string.format("Tag Assignments: %d", metrics.basic.TagAssignments))
      print(string.format("Cache Hit Rate: %.2f%%", metrics.cacheHitRate * 100))
      print(string.format("Average Validation Time: %.2fms", metrics.averageValidationTime))
      print(string.format("Error Rate: %.4f errors/second", metrics.errorRate))
    elseif command == "/tbrds refresh" then
      debugLog(string.format("Tag refresh requested by %s", player.Name))
      local tagService = ServiceManager.GetService("Tag") :: Types.TagService?
      if tagService then
        local refreshedCount: number = 0
        for _, targetPlayer: Player in ipairs(Players:GetPlayers()) do
          if targetPlayer.Parent then
            tagService.RefreshPlayerTag(targetPlayer)
            refreshedCount += 1
          end
        end
        debugLog(string.format("Refreshed tags for %d players", refreshedCount))
      end
    end
  end

  -- Connect new players
  Players.PlayerAdded:Connect(function(player: Player): ()
    player.Chatted:Connect(function(message: string): ()
      handleAdminCommands(player, message)
    end)
  end)

  -- Handle existing players
  for _, player: Player in ipairs(Players:GetPlayers()) do
    if player.Parent then
      player.Chatted:Connect(function(message: string): ()
        handleAdminCommands(player, message)
      end)
    end
  end

  debugLog("System monitoring enabled")
end

-- =============================================================================
-- ERROR HANDLING
-- =============================================================================
--[[
    Sets up error handling and recovery mechanisms, including periodic health checks.
]]
local function setupErrorHandling(): ()
  -- Periodic health check
  task.spawn(function(): ()
    while isSystemRunning do
      task.wait(60) -- Check every minute
      if not ServiceManager.AreAllServicesHealthy() then
        warn("TBRDS: Service health check failed, attempting recovery...")
        if ServiceManager.EmergencyRecovery() then
          debugLog("Automatic recovery successful")
        else
          warn("TBRDS: Automatic recovery failed - manual intervention required")
        end
      end
    end
  end)

  -- Graceful shutdown
  game:BindToClose(function(): ()
    debugLog("Game shutting down, cleaning up TBRDS...")
    isSystemRunning = false
    ServiceManager.Shutdown()
  end)

  debugLog("Error handling and recovery systems enabled")
end

-- =============================================================================
-- SYSTEM INTEGRATION
-- =============================================================================
--[[
    Sets up integration with other systems via event subscriptions.
]]
local function setupSystemIntegration(): ()
  local configService = ServiceManager.GetService("Configuration") :: Types.ConfigurationService?
  if not configService then
    debugLog("System integration skipped: Configuration service unavailable")
    return
  end
  local config = configService.GetConfiguration()
  if not config or not config.Integration or not config.Integration.BroadcastToOtherSystems then
    debugLog("System integration skipped: Integration not enabled or configuration invalid")
    return
  end

  if not EventSystem then
    debugLog("System integration skipped: EventSystem unavailable")
    return
  end

  -- Subscribe to tag change events
  EventSystem.Subscribe("TagChanged", function(eventData: Types.TagEventData): ()
    debugLog(
      string.format(
        "Tag changed: %s -> %s for %s",
        eventData.OldRole or "None",
        eventData.NewRole,
        eventData.Player.Name
      )
    )
    -- Potential integration with MCS, CGS, GSF, etc.
  end)

  -- Subscribe to security violations
  EventSystem.Subscribe("SecurityViolation", function(eventData: Types.TagEventData): ()
    warn(
      string.format("TBRDS Security Violation: %s for %s", eventData.NewRole, eventData.Player.Name)
    )
    -- Potential integration with anti-cheat systems
  end)

  debugLog("System integration enabled")
end

-- =============================================================================
-- MAIN IMPLEMENTATION
-- =============================================================================
debugLog("TBRDS Server starting...")

-- Initialize the system
if not initializeSystem() then
  error("TBRDS: Failed to initialize system - aborting startup")
end

-- Set up monitoring, error handling, and integration
setupSystemMonitoring()
setupErrorHandling()
setupSystemIntegration()

-- Mark system as running
isSystemRunning = true

-- Log final status report
local finalReport: string = ServiceManager.GetSystemHealthReport()
debugLog("=== TBRDS Startup Complete ===")
print(finalReport)

-- =============================================================================
-- SYSTEM INTERFACE
-- =============================================================================

-- Store interface in ServerStorage
local TBRDSFolder: Folder
local existingTBRDS = ServerStorage:FindFirstChild("TBRDS")
if existingTBRDS and existingTBRDS:IsA("Folder") then
  TBRDSFolder = existingTBRDS :: Folder
else
  TBRDSFolder = Instance.new("Folder")
  TBRDSFolder.Name = "TBRDS"
  TBRDSFolder.Parent = ServerStorage
end

-- Debug logs
debugLog("TBRDS Server initialization complete!")
