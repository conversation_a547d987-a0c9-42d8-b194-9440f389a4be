--!strict

--[[
    Stam.luau
	
	- version: 1.1.0
	- author: z
	- contributors: Bleck

	- description:
		- Stamina Prototype system
	
]]

-- Services
local Players: Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Player Reference
local LocalPlayer = Players.LocalPlayer

-- Modules
local Types = require(ReplicatedStorage.TBRDS.TBRDS_Types) :: any
local Utils = require(ReplicatedStorage.TBRDS.Shared.TBRDS_Utils)
local TBRDSConstants = Types.TBRDSConstants :: { REMOTE_NAMES: { TagUpdate: string } }

-- Remotes
local TBRDS = ReplicatedStorage:WaitForChild("TBRDS") :: Folder
local Remotes = TBRDS:WaitForChild("Remotes") :: Folder
local TagRemote = Remotes:WaitForChild(TBRDSConstants.REMOTE_NAMES.TagUpdate) :: RemoteEvent
local TagRequestRemote = Remotes:WaitForChild("TagRequestRemote") :: RemoteEvent

-- Debug logging
local function debugLog(message: string)
  Utils.print("Client", message)
end

-- Handle tag updates from server
TagRemote.OnClientEvent:Connect(function(player: Player, tag: string, _style: string)
  if player == LocalPlayer then
    -- This is handled by the server
    return
  end

  -- This is now just for verification/debugging
  debugLog(string.format("Received tag update for %s: %s", player.Name, tag))
end)

-- Request our tag when character loads
if LocalPlayer then
  LocalPlayer.CharacterAdded:Connect(function()
    TagRequestRemote:FireServer()
    debugLog("Requested tag from server")
  end)
end

-- Request tag when we join
if LocalPlayer then
  if LocalPlayer.Character then
    TagRequestRemote:FireServer()
    debugLog("Requested tag from server (initial)")
  end
end
