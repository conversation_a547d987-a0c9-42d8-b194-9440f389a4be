--!strict

--[[
    - file: MCS_Middleware_Initialization.luau

    - version: 2.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Middleware System for the Modular Command System (MCS)
      - Handles comprehensive pre-processing for commands with robust error handling
      - Features:
        - Authentication (verify player identity and session)
        - Authorization (check permissions and role-based access)
        - Analytics (record command usage and performance metrics)
        - Rate limiting (prevent command spam and abuse)
        - Input validation (sanitize and verify arguments)
        - Logging (detailed audit trails and debugging)
      - Implements middleware chain pattern with fallback mechanisms
      - Supports dynamic middleware registration and hot-swapping
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Types = require(ReplicatedStorage:WaitForChild("MCS"):WaitForChild("MCS_Types"))
local Utils =
  require(ReplicatedStorage:WaitForChild("MCS"):WaitForChild("Shared"):WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local MODULE_NAME = "MiddlewareSystem"

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local Middleware = {}

-- ============================================================================
-- VARIABLES
-- ============================================================================
local middlewareChain: { Types.MiddlewareModule } = {}
local isInitialized: boolean = false
local initializationInProgress: boolean = false
local chainStats: Types.MiddlewareChainStats = {
  totalExecutions = 0,
  successfulExecutions = 0,
  failedExecutions = 0,
  averageExecutionTime = 0,
  lastExecutionTime = 0,
}

-- Fallback middleware implementations
local fallbackMiddleware: { [string]: Types.MiddlewareModule } = {}

-- Helper function to get ModuleScript
local function getModuleScript(parent: Instance, name: string): ModuleScript?
  local child = parent:FindFirstChild(name)
  if child and child:IsA("ModuleScript") then
    return child :: ModuleScript
  end
  return nil
end

-- ============================================================================
-- PRIVATE FUNCTIONS
-- ============================================================================

-- Create fallback middleware implementations
local function createFallbackMiddleware()
  Utils.print(MODULE_NAME, "Creating fallback middleware implementations...")

  -- Basic Authentication Fallback
  fallbackMiddleware.Authentication = {
    name = "Authentication_Fallback",
    priority = 1,
    enabled = true,
    process = function(
      player: Player,
      commandName: string,
      args: { string }?
    ): (boolean, string?, any?)
      -- Basic player validation
      if not player or not player.Parent then
        return false, "Player is not valid or has left the game"
      end

      -- Check if player is properly loaded
      if not player.Character then
        return false, "Player character is not loaded"
      end

      Utils.print(MODULE_NAME, string.format("Authentication passed for %s", player.Name))
      return true
    end,
  } :: Types.MiddlewareModule

  -- Basic Permission Fallback
  fallbackMiddleware.Permission = {
    name = "Permission_Fallback",
    priority = 2,
    enabled = true,
    process = function(
      player: Player,
      commandName: string,
      args: { string }?
    ): (boolean, string?, any?)
      -- Basic permission check - allow all for fallback
      -- In production, this should connect to your permission system
      Utils.print(
        MODULE_NAME,
        string.format("Permission check passed for %s using %s", player.Name, commandName)
      )
      return true
    end,
  } :: Types.MiddlewareModule

  -- Rate Limiter Fallback with state
  local rateLimits: { [number]: { lastCommand: number, commandCount: number, violations: number } } =
    {}
  fallbackMiddleware.RateLimiter = {
    name = "RateLimiter_Fallback",
    priority = 3,
    enabled = true,
    process = function(
      player: Player,
      commandName: string,
      args: { string }?
    ): (boolean, string?, any?)
      local userId = player.UserId
      local currentTime = os.time()

      if not rateLimits[userId] then
        rateLimits[userId] = {
          lastCommand = currentTime,
          commandCount = 0,
          violations = 0,
        }
        return true
      end

      local playerLimit = rateLimits[userId]

      if currentTime - playerLimit.lastCommand > 10 then
        playerLimit.commandCount = 0
        playerLimit.violations = math.max(0, playerLimit.violations - 1)
      end

      playerLimit.commandCount += 1
      playerLimit.lastCommand = currentTime

      local maxCommands = math.max(3, 8 - playerLimit.violations * 2)

      if playerLimit.commandCount > maxCommands then
        playerLimit.violations += 1
        local cooldownTime = math.min(60, 5 * playerLimit.violations)

        Utils.print(
          MODULE_NAME,
          string.format(
            "Rate limit exceeded for %s (Violation #%d)",
            player.Name,
            playerLimit.violations
          )
        )
        return false,
          string.format(
            "Rate limit exceeded. Please wait %d seconds before using commands.",
            cooldownTime
          )
      end

      return true
    end,
  } :: Types.MiddlewareModule

  -- Logger Fallback
  fallbackMiddleware.Logger = {
    name = "Logger_Fallback",
    priority = 4,
    enabled = true,
    process = function(
      player: Player,
      commandName: string,
      args: { string }?
    ): (boolean, string?, any?)
      local timestamp = os.date("%Y-%m-%d %H:%M:%S", os.time())
      local argsString = args and table.concat(args, ", ") or "none"

      -- Sanitize arguments for logging
      argsString = Utils.sanitize(argsString)

      local logMessage = string.format(
        "[%s] Player: %s (%d) | Command: %s | Args: [%s]",
        timestamp,
        player.Name,
        player.UserId,
        commandName,
        argsString
      )

      Utils.print("CommandLog", logMessage)
      return true
    end,
  } :: Types.MiddlewareModule

  -- Input Validation Fallback
  fallbackMiddleware.InputValidation = {
    name = "InputValidation_Fallback",
    priority = 5,
    enabled = true,
    process = function(
      player: Player,
      commandName: string,
      args: { string }?
    ): (boolean, string?, any?)
      -- Validate command name
      if not commandName or commandName == "" then
        return false, "Invalid command name"
      end

      -- Sanitize command name
      commandName = Utils.sanitize(commandName)

      -- Validate command length
      if
        #commandName > (Configuration.Commands and Configuration.Commands.MAX_COMMAND_LENGTH or 50)
      then
        return false, "Command name too long"
      end

      -- Validate arguments if present
      if args then
        local maxArgs = Configuration.Commands and Configuration.Commands.MAX_ARGS or 10
        if #args > maxArgs then
          return false, string.format("Too many arguments. Maximum allowed: %d", maxArgs)
        end

        -- Sanitize all arguments
        for i, arg in ipairs(args) do
          args[i] = Utils.sanitize(arg)
        end
      end

      return true, nil, { sanitizedCommand = commandName, sanitizedArgs = args }
    end,
  } :: Types.MiddlewareModule

  Utils.print(MODULE_NAME, "Fallback middleware created successfully")
end

-- Load individual middleware module with error handling
local function loadMiddlewareModule(
  moduleName: string,
  moduleScript: ModuleScript,
  priority: number
): Types.MiddlewareModule?
  Utils.startTimer("LoadMiddleware: " .. moduleName)

  local success, moduleOrError = pcall(function()
    return require(moduleScript) :: any -- Type assertion to suppress require error
  end)

  if not success then
    Utils.print(
      MODULE_NAME,
      string.format("Failed to load %s middleware: %s", moduleName, tostring(moduleOrError))
    )
    Utils.endTimer(MODULE_NAME, "LoadMiddleware: " .. moduleName)
    return nil
  end

  local middlewareModule = moduleOrError

  -- Validate middleware structure
  if type(middlewareModule) ~= "table" then
    Utils.print(MODULE_NAME, string.format("%s middleware is not a valid table", moduleName))
    Utils.endTimer(MODULE_NAME, "LoadMiddleware: " .. moduleName)
    return nil
  end

  if type(middlewareModule.process) ~= "function" then
    Utils.print(MODULE_NAME, string.format("%s middleware missing process function", moduleName))
    Utils.endTimer(MODULE_NAME, "LoadMiddleware: " .. moduleName)
    return nil
  end

  -- Validate optional fields
  if middlewareModule.init and type(middlewareModule.init) ~= "function" then
    Utils.print(MODULE_NAME, string.format("%s middleware has invalid init function", moduleName))
    Utils.endTimer(MODULE_NAME, "LoadMiddleware: " .. moduleName)
    return nil
  end

  if middlewareModule.cleanup and type(middlewareModule.cleanup) ~= "function" then
    Utils.print(
      MODULE_NAME,
      string.format("%s middleware has invalid cleanup function", moduleName)
    )
    Utils.endTimer(MODULE_NAME, "LoadMiddleware: " .. moduleName)
    return nil
  end

  if middlewareModule.getStatistics and type(middlewareModule.getStatistics) ~= "function" then
    Utils.print(
      MODULE_NAME,
      string.format("%s middleware has invalid getStatistics function", moduleName)
    )
    Utils.endTimer(MODULE_NAME, "LoadMiddleware: " .. moduleName)
    return nil
  end

  if middlewareModule.forceCleanup and type(middlewareModule.forceCleanup) ~= "function" then
    Utils.print(
      MODULE_NAME,
      string.format("%s middleware has invalid forceCleanup function", moduleName)
    )
    Utils.endTimer(MODULE_NAME, "LoadMiddleware: " .. moduleName)
    return nil
  end

  if
    middlewareModule.setCommandRateLimit
    and type(middlewareModule.setCommandRateLimit) ~= "function"
  then
    Utils.print(
      MODULE_NAME,
      string.format("%s middleware has invalid setCommandRateLimit function", moduleName)
    )
    Utils.endTimer(MODULE_NAME, "LoadMiddleware: " .. moduleName)
    return nil
  end

  if middlewareModule.version and type(middlewareModule.version) ~= "string" then
    Utils.print(MODULE_NAME, string.format("%s middleware has invalid version", moduleName))
    Utils.endTimer(MODULE_NAME, "LoadMiddleware: " .. moduleName)
    return nil
  end

  -- Validate and type init function
  local initFunction: (() -> boolean)? = nil
  if middlewareModule.init then
    initFunction = middlewareModule.init :: () -> boolean
  end

  -- Create standardized middleware structure
  local standardizedMiddleware: Types.MiddlewareModule = {
    name = moduleName,
    priority = priority,
    enabled = true,
    process = middlewareModule.process :: (
      player: Player,
      commandName: string,
      args: { string }?
    ) -> (boolean, string?, any?),
    init = initFunction,
    cleanup = middlewareModule.cleanup :: (() -> ())?,
    version = middlewareModule.version or "1.0.0",
    getStatistics = middlewareModule.getStatistics :: (() -> { [string]: any })?,
    forceCleanup = middlewareModule.forceCleanup :: (() -> ())?,
    setCommandRateLimit = middlewareModule.setCommandRateLimit :: ((string, Types.RateLimitConfig) -> ())?,
  }

  -- Initialize middleware if it has an init function
  if standardizedMiddleware.init then
    local initSuccess, initError = pcall(function()
      return standardizedMiddleware.init()
    end)
    if not initSuccess then
      Utils.print(
        MODULE_NAME,
        string.format("Failed to initialize %s middleware: %s", moduleName, tostring(initError))
      )
      Utils.endTimer(MODULE_NAME, "LoadMiddleware: " .. moduleName)
      return nil
    end
  end

  Utils.print(
    MODULE_NAME,
    string.format(
      "%s middleware loaded successfully (v%s)",
      moduleName,
      standardizedMiddleware.version or "Unknown"
    )
  )

  Utils.endTimer(MODULE_NAME, "LoadMiddleware: " .. moduleName)
  return standardizedMiddleware
end

-- Load all middleware modules with fallback support
local function loadMiddlewareChain(): { Types.MiddlewareModule }
  Utils.startTimer("LoadMiddlewareChain")
  Utils.print(MODULE_NAME, "Loading middleware chain...")

  local chain: { Types.MiddlewareModule } = {}

  -- Define middleware loading configuration
  local middlewareConfig = {
    {
      name = "Authentication",
      path = getModuleScript(script.Parent, "MCS_Middleware_Auth"),
      priority = 1,
    },
    {
      name = "Permission",
      path = getModuleScript(script.Parent, "MCS_Middleware_Permission"), -- Updated path
      priority = 2,
    },
    {
      name = "RateLimiter",
      path = getModuleScript(script.Parent, "MCS_Middleware_RateLimiter"),
      priority = 3,
    },
    {
      name = "Logger",
      path = getModuleScript(script.Parent, "MCS_Middleware_Logger"),
      priority = 4,
    },
    {
      name = "Analytics",
      path = getModuleScript(script.Parent, "MCS_Middleware_Analytics"),
      priority = 5,
    },
    {
      name = "InputValidation",
      path = getModuleScript(script.Parent, "MCS_Middleware_InputValidation"),
      priority = 6,
    },
  }

  for _, config in ipairs(middlewareConfig) do
    local middleware: Types.MiddlewareModule?

    if config.path then
      middleware = loadMiddlewareModule(config.name, config.path, config.priority)
    end

    -- Use fallback if main middleware failed to load
    if not middleware and fallbackMiddleware[config.name] then
      Utils.print(MODULE_NAME, string.format("Using fallback for %s middleware", config.name))
      middleware = fallbackMiddleware[config.name]
      middleware.priority = config.priority
    end

    if middleware then
      table.insert(chain, middleware)
    else
      Utils.print(
        MODULE_NAME,
        string.format("No middleware available for %s (skipping)", config.name)
      )
    end
  end

  -- Sort middleware by priority
  table.sort(chain, function(a, b)
    return a.priority < b.priority
  end)

  Utils.print(MODULE_NAME, string.format("Middleware chain loaded with %d modules", #chain))
  Utils.endTimer(MODULE_NAME, "LoadMiddlewareChain")
  return chain
end

-- Execute middleware with timeout protection
local function executeMiddlewareWithTimeout(
  middleware: Types.MiddlewareModule,
  player: Player,
  commandName: string,
  args: { string }?
): (boolean, string?, any?)
  local startTime = os.clock()
  local completed = false
  local result: { any } = {}

  -- Get timeout from configuration
  local initializationConfig = Configuration.Middleware and Configuration.Middleware.Initialization
  local middlewareTimeout = if initializationConfig
      and type(initializationConfig.MIDDLEWARE_TIMEOUT) == "number"
    then initializationConfig.MIDDLEWARE_TIMEOUT
    else 2

  -- Execute middleware in a separate thread
  task.spawn(function()
    local success, successResult, errorMessage, data =
      pcall(middleware.process, player, commandName, args)

    if success then
      result = { successResult, errorMessage, data }
    else
      result = { false, string.format("Middleware error: %s", tostring(successResult)) }
    end
    completed = true
  end)

  -- Wait for completion or timeout
  while not completed and (os.clock() - startTime) < middlewareTimeout do
    task.wait(0.1)
  end

  if not completed then
    Utils.print(MODULE_NAME, string.format("Middleware %s timed out", middleware.name))
    return false, string.format("Middleware %s timed out", middleware.name)
  end

  return table.unpack(result)
end

-- Update chain statistics
local function updateChainStats(executionTime: number, success: boolean)
  chainStats.totalExecutions += 1
  chainStats.lastExecutionTime = os.time()

  if success then
    chainStats.successfulExecutions += 1
  else
    chainStats.failedExecutions += 1
  end

  -- Update average execution time
  local totalTime = chainStats.averageExecutionTime * (chainStats.totalExecutions - 1)
    + executionTime
  chainStats.averageExecutionTime = totalTime / chainStats.totalExecutions
end

-- ============================================================================
-- PUBLIC FUNCTIONS
-- ============================================================================

-- Initialize the middleware system
function Middleware.init(): boolean
  if isInitialized then
    Utils.print(MODULE_NAME, "Middleware system already initialized")
    return true
  end

  if initializationInProgress then
    Utils.print(MODULE_NAME, "Middleware initialization already in progress")
    return false
  end

  -- Get configuration with proper nil handling
  local initializationConfig = Configuration.Middleware and Configuration.Middleware.Initialization

  if not initializationConfig then
    Utils.print(MODULE_NAME, "Analytics configuration missing - disabling module")
    return false
  end

  initializationInProgress = true
  Utils.startTimer("MiddlewareInit")
  Utils.print(MODULE_NAME, "Initializing middleware system...")

  -- Retrieve and validate numeric configuration values with defaults
  local saveInterval: number = if type(initializationConfig.MAX_MIDDLEWARE_EXECUTION_TIME)
      == "number"
    then initializationConfig.MAX_MIDDLEWARE_EXECUTION_TIME
    else 3
  local maxRetries: number = if type(initializationConfig.MAX_RETRIES) == "number"
    then initializationConfig.MAX_RETRIES
    else 3
  local timout: number = if type(initializationConfig.MIDDLEWARE_TIMEOUT) == "number"
    then initializationConfig.MIDDLEWARE_TIMEOUT
    else 2

  -- Create fallback middleware first
  createFallbackMiddleware()

  -- Load the middleware chain
  middlewareChain = loadMiddlewareChain()

  if #middlewareChain == 0 then
    Utils.print(MODULE_NAME, "Warning: No middleware modules loaded")
    initializationInProgress = false
    Utils.endTimer(MODULE_NAME, "MiddlewareInit")
    return false
  end

  isInitialized = true
  initializationInProgress = false

  Utils.print(
    MODULE_NAME,
    string.format("Middleware system initialized successfully with %d modules", #middlewareChain)
  )
  Utils.endTimer(MODULE_NAME, "MiddlewareInit")
  return true
end

-- Execute the middleware chain
function Middleware.run(
  player: Player,
  commandName: string,
  args: { string }?
): (boolean, string?, any?)
  -- Ensure initialization
  if not isInitialized and not Middleware.init() then
    return false, "Failed to initialize middleware system"
  end

  Utils.startTimer("MiddlewareExecution")

  -- Sanitize inputs
  if not player or not player.Parent then
    Utils.endTimer(MODULE_NAME, "MiddlewareExecution")
    return false, "Invalid player"
  end

  -- Validate and sanitize commandName
  local sanitizedCommandName: string
  if type(commandName) == "string" and commandName ~= "" then
    sanitizedCommandName = Utils.sanitize(commandName)
  else
    Utils.endTimer(MODULE_NAME, "MiddlewareExecution")
    return false, "Invalid command name"
  end

  if sanitizedCommandName == "" then
    Utils.endTimer(MODULE_NAME, "MiddlewareExecution")
    return false, "Invalid command name"
  end

  -- Ensure args is a table
  args = args or {}

  -- Debug information
  if Configuration.Settings.DebugMode then
    Utils.print(
      MODULE_NAME,
      string.format(
        "Running middleware chain for %s using command: %s",
        player.Name,
        sanitizedCommandName
      )
    )
  end

  local chainStartTime = os.clock()
  local aggregatedData = {}

  -- Execute each middleware in the chain
  for i, middleware in ipairs(middlewareChain) do
    if not middleware.enabled then
      if Configuration.Settings.DebugMode then
        Utils.print(MODULE_NAME, string.format("Skipping disabled middleware: %s", middleware.name))
      end
      continue
    end

    local middlewareStartTime = os.clock()
    local success, errorMessage, data =
      executeMiddlewareWithTimeout(middleware, player, commandName, args)
    local middlewareExecutionTime = (os.clock() - middlewareStartTime) * 1000

    -- Log middleware execution if debug mode is enabled
    if Configuration.Settings.DebugMode then
      Utils.print(
        MODULE_NAME,
        string.format(
          "Middleware %s executed in %.2f ms (Success: %s)",
          middleware.name,
          middlewareExecutionTime,
          tostring(success)
        )
      )
    end

    -- Handle middleware failure
    if not success then
      local totalExecutionTime = (os.clock() - chainStartTime) * 1000
      updateChainStats(totalExecutionTime, false)

      Utils.print(
        MODULE_NAME,
        string.format(
          "Middleware %s rejected command: %s",
          middleware.name,
          errorMessage or "No reason provided"
        )
      )
      Utils.endTimer(MODULE_NAME, "MiddlewareExecution")
      return false, errorMessage or string.format("Middleware %s check failed", middleware.name)
    end

    -- Aggregate data from middleware
    if data then
      aggregatedData[middleware.name] = data
    end
  end

  -- All middleware passed
  local totalExecutionTime = (os.clock() - chainStartTime) * 1000
  updateChainStats(totalExecutionTime, true)

  if Configuration.Settings.DebugMode then
    Utils.print(
      MODULE_NAME,
      string.format("Middleware chain completed successfully in %.2f ms", totalExecutionTime)
    )
  end

  Utils.endTimer(MODULE_NAME, "MiddlewareExecution")
  return true, nil, aggregatedData
end

-- Register a new middleware in the chain
function Middleware.register(middlewareModule: Types.MiddlewareModule): boolean
  if not isInitialized then
    Utils.print(MODULE_NAME, "Cannot register middleware: system not initialized")
    return false
  end

  -- Validate middleware structure
  if type(middlewareModule) ~= "table" or type(middlewareModule.process) ~= "function" then
    Utils.print(MODULE_NAME, "Invalid middleware module provided")
    return false
  end

  -- Set default values
  middlewareModule.priority = middlewareModule.priority or (#middlewareChain + 1)
  middlewareModule.enabled = middlewareModule.enabled ~= false -- Default to true
  middlewareModule.name = middlewareModule.name
    or "CustomMiddleware_" .. tostring(#middlewareChain + 1)

  table.insert(middlewareChain, middlewareModule)

  -- Re-sort by priority
  table.sort(middlewareChain, function(a, b)
    return a.priority < b.priority
  end)

  Utils.print(MODULE_NAME, string.format("Registered middleware: %s", middlewareModule.name))
  return true
end

-- Enable/disable specific middleware
function Middleware.setMiddlewareEnabled(middlewareName: string, enabled: boolean): boolean
  for _, middleware in ipairs(middlewareChain) do
    if middleware.name == middlewareName then
      middleware.enabled = enabled
      Utils.print(
        MODULE_NAME,
        string.format("Middleware %s %s", middlewareName, enabled and "enabled" or "disabled")
      )
      return true
    end
  end

  Utils.print(MODULE_NAME, string.format("Middleware %s not found", middlewareName))
  return false
end

-- Get middleware chain information
function Middleware.getChainInfo(): { Types.Middleware }
  local info: { Types.Middleware } = {}

  for _, middleware in ipairs(middlewareChain) do
    table.insert(info, {
      name = middleware.name,
      priority = middleware.priority,
      enabled = middleware.enabled,
      version = middleware.version or "Unknown",
    })
  end

  return info
end

-- Get middleware chain statistics
function Middleware.getChainStats(): Types.MiddlewareChainStats
  return {
    totalExecutions = chainStats.totalExecutions,
    successfulExecutions = chainStats.successfulExecutions,
    failedExecutions = chainStats.failedExecutions,
    averageExecutionTime = chainStats.averageExecutionTime,
    lastExecutionTime = chainStats.lastExecutionTime,
  }
end

-- Cleanup middleware system
function Middleware.cleanup()
  Utils.print(MODULE_NAME, "Cleaning up middleware system...")

  -- Call cleanup functions for each middleware
  for _, middleware in ipairs(middlewareChain) do
    if middleware.cleanup then
      pcall(middleware.cleanup)
    end
  end

  middlewareChain = {}
  isInitialized = false

  Utils.print(MODULE_NAME, "Middleware system cleaned up")
end

-- Hot-reload middleware chain
function Middleware.reload(): boolean
  Utils.print(MODULE_NAME, "Reloading middleware system...")

  -- Cleanup existing middleware
  for _, middleware in ipairs(middlewareChain) do
    if middleware.cleanup then
      pcall(middleware.cleanup)
    end
  end

  Middleware.cleanup()
  createFallbackMiddleware()

  local success = Middleware.init()
  if success then
    Utils.print(MODULE_NAME, "Middleware system reloaded successfully")
  else
    Utils.print(MODULE_NAME, "Failed to reload middleware system")
  end

  return success
end

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

-- Auto-initialize on server start (optional)
if RunService:IsServer() then
  task.spawn(function()
    task.wait(1) -- Allow other systems to initialize first
    Middleware.init()
  end)
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Middleware
