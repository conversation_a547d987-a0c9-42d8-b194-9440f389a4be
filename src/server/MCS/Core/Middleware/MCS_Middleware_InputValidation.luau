--!strict

--[[
    - file: MCS_Middleware_InputValidation.luau
    
    - version: 1.0.0
    - author: BleckWolf25
    - contributors:
    
    - copyright: Dynamic Innovative Studio
    
    - description:
      - Input Validation Middleware for Modular Command System (MCS)
      - Sanitizes and validates command inputs to prevent malicious or invalid data
      - Ensures command name and arguments meet system requirements
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local MODULE_NAME = "InputValidation"

-- ============================================================================
-- VARIABLES
-- ============================================================================
local isInitialized: boolean = false

-- ============================================================================
-- MIDDLEWARE IMPLEMENTATION
-- ============================================================================
local InputValidation = {
  name = MODULE_NAME,
  priority = 6,
  enabled = true,
  version = "2.0.0",
} :: Types.MiddlewareModule

function InputValidation.init(): boolean
  Utils.startTimer(MODULE_NAME, "Initialization")
  isInitialized = true
  Utils.endTimer(MODULE_NAME, "Initialization")
  Utils.print(MODULE_NAME, "InputValidation middleware initialized successfully")
  return true
end

function InputValidation.process(
  player: Player,
  commandName: string,
  args: { string }?
): (boolean, string?, any?)
  if not isInitialized then
    return false, "InputValidation middleware not initialized", nil
  end

  Utils.startTimer(MODULE_NAME, "Process")

  -- Validate command name
  if not commandName or commandName == "" then
    Utils.endTimer(MODULE_NAME, "Process")
    return false, "Invalid command name", nil
  end

  -- Sanitize command name
  local sanitizedCommand = Utils.sanitize(commandName)
  if sanitizedCommand == "" then
    Utils.endTimer(MODULE_NAME, "Process")
    return false, "Invalid command name after sanitization", nil
  end

  -- Validate command length
  local maxCommandLength = Configuration.Commands.MAX_COMMAND_LENGTH or 50
  if #sanitizedCommand > maxCommandLength then
    Utils.endTimer(MODULE_NAME, "Process")
    return false, string.format("Command name too long (max %d characters)", maxCommandLength), nil
  end

  -- Validate arguments
  local sanitizedArgs = {}
  if args then
    local maxArgs = Configuration.Commands.MAX_ARGS or 10
    if #args > maxArgs then
      Utils.endTimer(MODULE_NAME, "Process")
      return false, string.format("Too many arguments (max %d)", maxArgs), nil
    end

    for i, arg in ipairs(args) do
      local sanitizedArg = Utils.sanitize(arg)
      if sanitizedArg == "" then
        Utils.endTimer(MODULE_NAME, "Process")
        return false, string.format("Invalid argument at position %d", i), nil
      end
      sanitizedArgs[i] = sanitizedArg
    end
  end

  Utils.endTimer(MODULE_NAME, "Process")
  return true, nil, { sanitizedCommand = sanitizedCommand, sanitizedArgs = sanitizedArgs }
end

function InputValidation.cleanup()
  Utils.print(MODULE_NAME, "Cleaning up input validation middleware")
  isInitialized = false
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return InputValidation
