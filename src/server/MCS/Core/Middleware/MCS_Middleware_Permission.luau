--!strict

--[[
    - file: MCS_Middleware_Permission.luau
    
    - version: 1.0.0
    - author: BleckWolf25
    - contributors:
    
    - copyright: Dynamic Innovative Studio
    
    - description:
      - Permission Middleware for Modular Command System (MCS)
      - Validates command permissions using PermissionService
      - Implements caching for performance optimization
      - Follows MCS middleware contract with standardized return types
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local ServerScriptService = game:GetService("ServerScriptService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Configuration = require(
  ReplicatedStorage.Configurations:WaitForChild("Systems"):WaitForChild("MCS_Configuration")
)
local PermissionService =
  require(ServerScriptService.MCS.Core:WaitForChild("MCS_PermissionService"))
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local MODULE_NAME = "Permission"
local CACHE_EXPIRY_TIME = 30 -- seconds

-- ============================================================================
-- VARIABLES
-- ============================================================================
local isInitialized: boolean = false
local permissionCache: { [number]: { [string]: { allowed: boolean, timestamp: number } } } = {}

-- ============================================================================
-- PRIVATE FUNCTIONS
-- ============================================================================

-- Clean expired cache entries
local function cleanupCache()
  local currentTime = os.time()
  for userId, commands in pairs(permissionCache) do
    for command, entry in pairs(commands) do
      if currentTime - entry.timestamp > CACHE_EXPIRY_TIME then
        commands[command] = nil
      end
    end
    if not next(commands) then
      permissionCache[userId] = nil
    end
  end
end

-- ============================================================================
-- MIDDLEWARE IMPLEMENTATION
-- ============================================================================
local Permission = {
  name = MODULE_NAME,
  priority = 2,
  enabled = true,
  version = "2.0.0",
} :: Types.MiddlewareModule

function Permission.init(): boolean
  Utils.startTimer(MODULE_NAME, "Initialization")

  if not PermissionService or not PermissionService.canUseCommand then
    Utils.print(MODULE_NAME, "ERROR: PermissionService not available or invalid")
    return false
  end

  isInitialized = true
  Utils.endTimer(MODULE_NAME, "Initialization")
  Utils.print(MODULE_NAME, "Permission middleware initialized successfully")
  return true
end

function Permission.process(
  player: Player,
  commandName: string,
  args: { string }?
): (boolean, string?, any?)
  if not isInitialized then
    return false, "Permission middleware not initialized", nil
  end

  Utils.startTimer(MODULE_NAME, "Process")

  -- Periodic cache cleanup
  cleanupCache()

  local userId = player.UserId
  local sanitizedCommand = Utils.sanitize(commandName)

  -- Check cache
  if permissionCache[userId] and permissionCache[userId][sanitizedCommand] then
    local entry = permissionCache[userId][sanitizedCommand]
    if os.time() - entry.timestamp <= CACHE_EXPIRY_TIME then
      Utils.print(
        MODULE_NAME,
        string.format("Using cached permission for %s: %s", player.Name, sanitizedCommand)
      )
      Utils.endTimer(MODULE_NAME, "Process")
      return entry.allowed, if entry.allowed then nil else "Permission denied (cached)", nil
    end
  end
  -- Perform permission check
  local success, canUse = pcall(function()
    return PermissionService.canUseCommand(player, sanitizedCommand)
  end)

  if not success then
    Utils.print(MODULE_NAME, string.format("Permission check error: %s", tostring(canUse)))
    Utils.endTimer(MODULE_NAME, "Process")
    return false, "Permission system error", nil
  end

  -- Cache result
  permissionCache[userId] = permissionCache[userId] or {}
  permissionCache[userId][sanitizedCommand] = {
    allowed = canUse,
    timestamp = os.time(),
  }

  if not canUse then
    Utils.print(
      MODULE_NAME,
      string.format("Permission denied for %s (%d) using %s", player.Name, userId, sanitizedCommand)
    )
    Utils.endTimer(MODULE_NAME, "Process")
    return false, "You don't have permission to use this command", nil
  end

  Utils.print(
    MODULE_NAME,
    string.format("Permission granted for %s (%d) using %s", player.Name, userId, sanitizedCommand)
  )
  Utils.endTimer(MODULE_NAME, "Process")
  return true, nil, { permissionLevel = PermissionService.getPermissionLevel(player) }
end

function Permission.cleanup()
  Utils.print(MODULE_NAME, "Cleaning up permission middleware")
  permissionCache = {}
  isInitialized = false
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return Permission
