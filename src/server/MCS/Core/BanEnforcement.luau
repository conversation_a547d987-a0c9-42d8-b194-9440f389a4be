--!strict

--[[
    - file: BanEnforcement.luau

    - version: 1.0.0
    - author: BleckWolf25
    - contributors:

    - copyright: Dynamic Innovative Studio

    - description:
      - Enforces persistent bans by checking DataStore on player join
      - Supports both public server and private server bans
	  - Integrates with MCS ban command
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(ReplicatedStorage.MCS:WaitForChild("MCS_Types"))
local Utils = require(ReplicatedStorage.MCS.Shared:WaitForChild("MCS_Utils"))

-- ============================================================================
-- CONSTANTS
-- ============================================================================
local MODULE_NAME = "BanEnforcement"
local BAN_DATASTORE_NAME = "MCS_Bans"
local PRIVATE_BAN_DATASTORE_NAME = "MCS_PrivateServerBans"

-- ============================================================================
-- VARIABLES
-- ============================================================================
local banStore: DataStore?
local privateBanStore: DataStore?
local isPrivateServer: boolean = game.PrivateServerId ~= ""

-- ============================================================================
-- INITIALIZATION
-- ============================================================================
local function initialize()
  local success: boolean, errorMsg: string? = pcall(function()
    banStore = DataStoreService:GetDataStore(BAN_DATASTORE_NAME)
    if isPrivateServer then
      privateBanStore = DataStoreService:GetDataStore(PRIVATE_BAN_DATASTORE_NAME)
    end
  end)
  if not success then
    Utils.print(
      MODULE_NAME,
      string.format("Failed to initialize ban DataStores: %s", errorMsg or "unknown error")
    )
  end
end

initialize()

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================
local function isPlayerBanned(userId: number): (boolean, string?, number?, string?)
  -- Check private server bans first if applicable
  if isPrivateServer and privateBanStore then
    local store: DataStore = privateBanStore
    local key = string.format("%s_%d", game.PrivateServerId, userId)
    local success, banData: Types.BanData? = pcall(function()
      return store:GetAsync(key)
    end)
    if success and banData then
      local banEnd = banData.endTime
      local reason = banData.reason
      if banEnd and os.time() < banEnd then
        return true, reason, banEnd, "Private Server"
      end
    end
  end

  -- Check public server bans
  if banStore then
    local store: DataStore = banStore
    local success, banData: Types.BanData? = pcall(function()
      return store:GetAsync(tostring(userId))
    end)
    if success and banData then
      local banEnd = banData.endTime
      local reason = banData.reason
      if banEnd and os.time() < banEnd then
        return true, reason, banEnd, "Public Server"
      end
    end
  end

  return false, nil, nil, nil
end

-- ============================================================================
-- PLAYER JOIN HANDLER
-- ============================================================================
Players.PlayerAdded:Connect(function(player: Player)
  local isBanned, reason, endTime, scope = isPlayerBanned(player.UserId)
  if isBanned then
    local kickMessage = string.format(
      "You are banned from this %s: %s%s",
      scope or "Server",
      reason or "No reason provided",
      endTime == math.huge and ""
        or string.format(" (Until: %s)", os.date("%Y-%m-%d %H:%M:%S", endTime))
    )
    local success = pcall(function()
      player:Kick(kickMessage)
    end)
    if success then
      Utils.print(
        MODULE_NAME,
        string.format(
          "Kicked banned player %s (%d): %s (Scope: %s)",
          player.Name,
          player.UserId,
          kickMessage,
          scope or "Unknown"
        )
      )
    else
      Utils.print(
        MODULE_NAME,
        string.format(
          "Failed to kick banned player %s (%d): %s",
          player.Name,
          player.UserId,
          kickMessage
        )
      )
    end
  end
end)

-- ============================================================================
-- EXPORTS
-- ============================================================================
return {}
