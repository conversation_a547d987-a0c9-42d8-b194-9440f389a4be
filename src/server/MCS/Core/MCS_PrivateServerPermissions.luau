--!strict

--[[
    - file: MCS_PrivateServerPermissions.luau
    - version: 1.0.0
    - author: BleckWolf25
    - contributors:
    - copyright: Dynamic Innovative Studio
    - description:
      - Private Server Permissions Module for the Modular Command System (MCS)
      - Features:
        - Set player permissions for private servers and allow normal users to use all MCS commands without affecting public servers
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local DataStoreService = game:GetService("DataStoreService")

-- ============================================================================
-- MODULES
-- ============================================================================
local Types = require(game.ReplicatedStorage.MCS.MCS_Types)

-- ============================================================================
-- MAIN IMPLEMENTATION
-- ============================================================================
local PrivateServerPermissions = {}

-- ============================================================================
-- VARIABLES
-- ============================================================================
local permissionsDataStore = DataStoreService:GetDataStore("MCS_PrivateServerPermissions")

-- ============================================================================
-- FUNCTIONS
-- ============================================================================

-- Set a player's permission level in a private server
function PrivateServerPermissions.SetPlayerPermission(
  privateServerId: string,
  userId: number,
  permissionLevel: number
)
  local key: string = privateServerId
  local permissions: Types.PermissionsData = permissionsDataStore:GetAsync(key) or {}
  permissions[userId] = permissionLevel
  local success: boolean, err: string? = pcall(
    function() -- No fix for now, this is luau new solver issue
      permissionsDataStore:SetAsync(key, permissions)
    end
  )
  if not success then
    warn("Failed to set permission: " .. tostring(err))
  end
end

-- Get a player's permission level in a private server
function PrivateServerPermissions.GetPlayerPermission(
  privateServerId: string,
  userId: number
): number?
  local key: string = privateServerId
  local permissions: Types.PermissionsData? = permissionsDataStore:GetAsync(key)
  if permissions then
    return permissions[userId]
  end
  return nil
end

-- Remove a player's custom permission in a private server
function PrivateServerPermissions.RemovePlayerPermission(privateServerId: string, userId: number)
  local key: string = privateServerId
  local permissions: Types.PermissionsData? = permissionsDataStore:GetAsync(key)
  if permissions and permissions[userId] then
    permissions[userId] = nil
    local success: boolean, err: string? = pcall(
      function() -- No fix for now, this is luau new solver issue
        permissionsDataStore:SetAsync(key, permissions)
      end
    )
    if not success then
      warn("Failed to remove permission: " .. tostring(err))
    end
  end
end

-- ============================================================================
-- EXPORTS
-- ============================================================================
return PrivateServerPermissions
