--!strict

--[[
    - file: MCS_Server_Initialization.luau
    
    - version: 2.0.0
    - author: BleckWolf25
    - contributors:
    
    - copyright: Dynamic Innovative Studio
    
    - description:
      - Server initialization script for the Modular Command System (MCS)
      - Loads and initializes all necessary MCS modules in the correct order
]]

-- ============================================================================
-- SERVICES
-- ============================================================================
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- ============================================================================
-- MODULE LOADING
-- ============================================================================
local CommandDispatcher = require(script.Parent.Core.MCS_CommandDispatcher)
local Configuration = require(ReplicatedStorage.Configurations.Systems.MCS_Configuration)
local Middleware = require(script.Parent.Core.Middleware.MCS_Middleware_Initialization)
local PermissionService = require(script.Parent.Core.MCS_PermissionService)
local Remotes = require(ReplicatedStorage:WaitForChild("MCS").MCS_Remotes)
local Types = require(ReplicatedStorage.MCS.MCS_Types)
local Utils = require(ReplicatedStorage:WaitForChild("MCS").Shared.MCS_Utils)

-- ============================================================================
-- INITIALIZATION MAIN FUNCTION
-- ============================================================================
local function init()
  Utils.print("MCS_Server_Initialization", "Starting initialization...")

  -- Initialize PermissionService
  local success: boolean, err: any = pcall(
    function() -- No fix for now, waiting until luau realeses new version for the new solver
      return PermissionService.init()
    end
  )
  if not success then
    warn("Failed to initialize PermissionService: " .. tostring(err))
  end

  -- Initialize Middleware
  success, err = pcall(function()
    return Middleware.init()
  end)
  if not success then
    warn("Failed to initialize Middleware: " .. tostring(err))
  end

  -- Initialize CommandDispatcher
  success, err = pcall(function()
    return CommandDispatcher.init()
  end)
  if not success then
    warn("Failed to initialize CommandDispatcher: " .. tostring(err))
  end

  -- Initialize Remotes
  success, err = pcall(function()
    return Remotes.init()
  end)
  if not success then
    warn("Failed to initialize Remotes: " .. tostring(err))
  end

  Utils.print("MCS_Server_Initialization", "Initialization completed.")
end

-- ============================================================================
-- INITIALIZE THE SYSTEM
-- ============================================================================
init()
