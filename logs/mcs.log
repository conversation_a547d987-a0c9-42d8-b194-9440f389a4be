  16:31:07.734  [MCS:MCS_Server_Initialization]: Starting initialization...  -  Server - MCS_Utils:65
  16:31:07.734  [MCS:PermissionService]: Initialized  -  Server - MCS_Utils:65
  16:31:07.734  [MCS:MiddlewareSystem]: Initializing middleware system...  -  Server - MCS_Utils:65
  16:31:07.734  [MCS:MiddlewareSystem]: Creating fallback middleware implementations...  -  Server - MCS_Utils:65
  16:31:07.734  [MCS:MiddlewareSystem]: Fallback middleware created successfully  -  Server - MCS_Utils:65
  16:31:07.734  [MCS:MiddlewareSystem]: Loading middleware chain...  -  Server - MCS_Utils:65
  16:31:07.734  [MCS:Authentication]: Authentication middleware initialized successfully  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:MiddlewareSystem]: Authentication middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:MiddlewareSystem]: LoadMiddleware: Authentication took 0.284 ms  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Authentication took 0.344 ms  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:Permission]: Permission middleware initialized successfully  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:MiddlewareSystem]: Permission middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:MiddlewareSystem]: LoadMiddleware: Permission took 0.201 ms  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Permission took 0.224 ms  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:RateLimiter]: RateLimiterInit took 0.004 ms  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:RateLimiter]: PerformanceMonitor: RateLimiterInit took 0.047 ms  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:RateLimiter]: Rate limiter middleware initialized successfully  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:MiddlewareSystem]: RateLimiter middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  16:31:07.735  [MCS:MiddlewareSystem]: LoadMiddleware: RateLimiter took 0.514 ms  -  Server - MCS_Utils:65
  16:31:07.736  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: RateLimiter took 0.571 ms  -  Server - MCS_Utils:65
  16:31:07.736  [MCS:Logger]: Logger initialized successfully  -  Server - MCS_Utils:65
  16:31:07.736  [MCS:MiddlewareSystem]: Logger middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  16:31:07.736  [MCS:MiddlewareSystem]: LoadMiddleware: Logger took 0.377 ms  -  Server - MCS_Utils:65
  16:31:07.736  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Logger took 0.462 ms  -  Server - MCS_Utils:65
  16:31:07.736  [MCS:Analytics]: Analytics initialized successfully  -  Server - MCS_Utils:65
  16:31:07.736  [MCS:MiddlewareSystem]: Analytics middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  16:31:07.736  [MCS:MiddlewareSystem]: LoadMiddleware: Analytics took 0.320 ms  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Analytics took 0.381 ms  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:InputValidation]: InputValidation middleware initialized successfully  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MiddlewareSystem]: InputValidation middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MiddlewareSystem]: LoadMiddleware: InputValidation took 0.206 ms  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: InputValidation took 0.238 ms  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MiddlewareSystem]: Middleware chain loaded with 6 modules  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MiddlewareSystem]: LoadMiddlewareChain took 2.774 ms  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddlewareChain took 2.858 ms  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MiddlewareSystem]: Middleware system initialized successfully with 6 modules  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MiddlewareSystem]: MiddlewareInit took 3.192 ms  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MiddlewareSystem]: PerformanceMonitor: MiddlewareInit took 3.220 ms  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MCS_Remotes]: Remotes system initialized successfully  -  Server - MCS_Utils:65
  16:31:07.737  [MCS:MCS_Server_Initialization]: Initialization completed.  -  Server - MCS_Utils:65
  16:31:07.876  [MCS:MCS_CommandParser]: Module initialized  -  Client - MCS_Utils:65
  16:31:07.877  [MCS:MCS_AutocompleteService]: Module initialized  -  Client - MCS_Utils:65
  16:31:12.053  [MCS:MiddlewareSystem]: Middleware system already initialized  -  Server - MCS_Utils:65
  16:31:12.165  [MCS:Console]: Initializing...  -  Client - MCS_Utils:65
  16:31:12.220  [MCS:FeedbackDisplay]: Initializing...  -  Client - MCS_Utils:65
  16:31:12.220  [MCS:MCS_AutocompleteService]: Initializing service  -  Client - MCS_Utils:65
  16:31:12.220  [MCS:MCS_AutocompleteService]: Service initialized successfully  -  Client - MCS_Utils:65
  16:31:12.220  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  16:31:12.220  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  16:31:12.220  [MCS:MCS_Client_Initialization]: MCS Client initialized  -  Client - MCS_Utils:65
  16:31:12.246  [MCS:MCS_Remotes]: Returned 2 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:12.274  [MCS:MCS_AutocompleteService]: Fetched 2 available commands  -  Client - MCS_Utils:65
  16:31:12.274  [MCS:AutocompleteService]: getAvailableCommands took 53.838 ms  -  Client - MCS_Utils:65
  16:31:12.274  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 53.965 ms  -  Client - MCS_Utils:65
  16:31:12.274  [MCS:MCS_AutocompleteService]: Returning 2 suggestions for prefix  -  Client - MCS_Utils:65
  16:31:12.274  [MCS:AutocompleteService]: getSuggestions took 54.347 ms  -  Client - MCS_Utils:65
  16:31:12.274  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 54.425 ms  -  Client - MCS_Utils:65
  16:31:35.674  [MCS:Console]: Console toggled via F2  -  Client - MCS_Utils:65
  16:31:37.006  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  16:31:37.006  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  16:31:37.038  [MCS:MCS_Remotes]: Returned 2 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:37.071  [MCS:MCS_AutocompleteService]: Fetched 2 available commands  -  Client - MCS_Utils:65
  16:31:37.071  [MCS:AutocompleteService]: getAvailableCommands took 64.712 ms  -  Client - MCS_Utils:65
  16:31:37.071  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 64.825 ms  -  Client - MCS_Utils:65
  16:31:37.071  [MCS:MCS_AutocompleteService]: Returning 2 suggestions for prefix  -  Client - MCS_Utils:65
  16:31:37.071  [MCS:AutocompleteService]: getSuggestions took 65.096 ms  -  Client - MCS_Utils:65
  16:31:37.072  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 65.262 ms  -  Client - MCS_Utils:65
  16:31:48.323  [MCS:MCS_AutocompleteService]: Getting suggestions for: !b  -  Client - MCS_Utils:65
  16:31:48.354  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:48.371  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:48.371  [MCS:AutocompleteService]: getSuggestions took 47.357 ms  -  Client - MCS_Utils:65
  16:31:48.371  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 47.511 ms  -  Client - MCS_Utils:65
  16:31:48.474  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ba  -  Client - MCS_Utils:65
  16:31:48.504  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:48.538  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:48.538  [MCS:AutocompleteService]: getSuggestions took 63.858 ms  -  Client - MCS_Utils:65
  16:31:48.538  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 64.055 ms  -  Client - MCS_Utils:65
  16:31:48.907  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban  -  Client - MCS_Utils:65
  16:31:48.937  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:48.971  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:48.971  [MCS:AutocompleteService]: getSuggestions took 63.803 ms  -  Client - MCS_Utils:65
  16:31:48.971  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 63.934 ms  -  Client - MCS_Utils:65
  16:31:49.406  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban   -  Client - MCS_Utils:65
  16:31:49.437  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:49.471  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:49.471  [MCS:AutocompleteService]: getSuggestions took 64.568 ms  -  Client - MCS_Utils:65
  16:31:49.471  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 64.707 ms  -  Client - MCS_Utils:65
  16:31:50.456  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1  -  Client - MCS_Utils:65
  16:31:50.487  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:50.504  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:50.504  [MCS:AutocompleteService]: getSuggestions took 47.925 ms  -  Client - MCS_Utils:65
  16:31:50.504  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 48.041 ms  -  Client - MCS_Utils:65
  16:31:50.824  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1X  -  Client - MCS_Utils:65
  16:31:50.854  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:50.888  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:50.888  [MCS:AutocompleteService]: getSuggestions took 63.503 ms  -  Client - MCS_Utils:65
  16:31:50.888  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 63.688 ms  -  Client - MCS_Utils:65
  16:31:51.006  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XF  -  Client - MCS_Utils:65
  16:31:51.037  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:51.054  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:51.054  [MCS:AutocompleteService]: getSuggestions took 48.316 ms  -  Client - MCS_Utils:65
  16:31:51.054  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 48.492 ms  -  Client - MCS_Utils:65
  16:31:51.258  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFP  -  Client - MCS_Utils:65
  16:31:51.287  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:51.321  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:51.321  [MCS:AutocompleteService]: getSuggestions took 63.026 ms  -  Client - MCS_Utils:65
  16:31:51.321  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 63.255 ms  -  Client - MCS_Utils:65
  16:31:51.372  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPA  -  Client - MCS_Utils:65
  16:31:51.404  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:51.437  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:51.437  [MCS:AutocompleteService]: getSuggestions took 65.559 ms  -  Client - MCS_Utils:65
  16:31:51.437  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 65.677 ms  -  Client - MCS_Utils:65
  16:31:51.523  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPAN  -  Client - MCS_Utils:65
  16:31:51.554  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:51.587  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:51.587  [MCS:AutocompleteService]: getSuggestions took 64.649 ms  -  Client - MCS_Utils:65
  16:31:51.587  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 64.703 ms  -  Client - MCS_Utils:65
  16:31:51.624  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPAND  -  Client - MCS_Utils:65
  16:31:51.654  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:51.671  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:51.671  [MCS:AutocompleteService]: getSuggestions took 46.611 ms  -  Client - MCS_Utils:65
  16:31:51.671  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 46.771 ms  -  Client - MCS_Utils:65
  16:31:51.706  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDA  -  Client - MCS_Utils:65
  16:31:51.737  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:51.771  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:51.771  [MCS:AutocompleteService]: getSuggestions took 64.820 ms  -  Client - MCS_Utils:65
  16:31:51.771  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 64.976 ms  -  Client - MCS_Utils:65
  16:31:52.124  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAF  -  Client - MCS_Utils:65
  16:31:52.154  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:52.171  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:52.171  [MCS:AutocompleteService]: getSuggestions took 47.378 ms  -  Client - MCS_Utils:65
  16:31:52.171  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 47.550 ms  -  Client - MCS_Utils:65
  16:31:52.357  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX  -  Client - MCS_Utils:65
  16:31:52.387  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:52.404  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:52.404  [MCS:AutocompleteService]: getSuggestions took 47.463 ms  -  Client - MCS_Utils:65
  16:31:52.405  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 47.615 ms  -  Client - MCS_Utils:65
  16:31:52.557  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban 1XFPANDAFX1  -  Client - MCS_Utils:65
  16:31:52.587  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:52.604  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:52.604  [MCS:AutocompleteService]: getSuggestions took 47.282 ms  -  Client - MCS_Utils:65
  16:31:52.605  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 47.554 ms  -  Client - MCS_Utils:65
  16:31:53.590  [MCS:MCS_AutocompleteService]: Getting suggestions for: !ban  -  Client - MCS_Utils:65
  16:31:53.621  [MCS:MCS_Remotes]: Returned 0 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:53.637  [MCS:MCS_AutocompleteService]: Fetched 0 suggestions from server  -  Client - MCS_Utils:65
  16:31:53.638  [MCS:AutocompleteService]: getSuggestions took 47.746 ms  -  Client - MCS_Utils:65
  16:31:53.638  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 47.857 ms  -  Client - MCS_Utils:65
  16:31:53.772  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  16:31:53.772  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  16:31:53.804  [MCS:MCS_Remotes]: Returned 2 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:53.821  [MCS:MCS_AutocompleteService]: Fetched 2 available commands  -  Client - MCS_Utils:65
  16:31:53.821  [MCS:AutocompleteService]: getAvailableCommands took 48.863 ms  -  Client - MCS_Utils:65
  16:31:53.822  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 49.004 ms  -  Client - MCS_Utils:65
  16:31:53.822  [MCS:MCS_AutocompleteService]: Returning 2 suggestions for prefix  -  Client - MCS_Utils:65
  16:31:53.822  [MCS:AutocompleteService]: getSuggestions took 49.421 ms  -  Client - MCS_Utils:65
  16:31:53.822  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 49.574 ms  -  Client - MCS_Utils:65
  16:31:54.543  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  16:31:54.543  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  16:31:54.571  [MCS:MCS_Remotes]: Returned 2 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:54.587  [MCS:MCS_AutocompleteService]: Fetched 2 available commands  -  Client - MCS_Utils:65
  16:31:54.587  [MCS:AutocompleteService]: getAvailableCommands took 43.888 ms  -  Client - MCS_Utils:65
  16:31:54.588  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 43.970 ms  -  Client - MCS_Utils:65
  16:31:54.588  [MCS:MCS_AutocompleteService]: Returning 2 suggestions for prefix  -  Client - MCS_Utils:65
  16:31:54.588  [MCS:AutocompleteService]: getSuggestions took 44.208 ms  -  Client - MCS_Utils:65
  16:31:54.588  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 44.284 ms  -  Client - MCS_Utils:65
  16:31:58.039  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm  -  Client - MCS_Utils:65
  16:31:58.070  [MCS:MCS_Remotes]: Returned 1 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:31:58.104  [MCS:MCS_AutocompleteService]: Fetched 1 suggestions from server  -  Client - MCS_Utils:65
  16:31:58.104  [MCS:AutocompleteService]: getSuggestions took 65.022 ms  -  Client - MCS_Utils:65
  16:31:58.105  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 65.332 ms  -  Client - MCS_Utils:65
  16:31:59.223  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm   -  Client - MCS_Utils:65
  16:31:59.223  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:31:59.223  [MCS:AutocompleteService]: getSuggestions took 0.041 ms  -  Client - MCS_Utils:65
  16:31:59.223  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.125 ms  -  Client - MCS_Utils:65
  16:32:00.190  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1  -  Client - MCS_Utils:65
  16:32:00.191  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:00.191  [MCS:AutocompleteService]: getSuggestions took 0.158 ms  -  Client - MCS_Utils:65
  16:32:00.191  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.315 ms  -  Client - MCS_Utils:65
  16:32:00.706  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1X  -  Client - MCS_Utils:65
  16:32:00.706  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:00.706  [MCS:AutocompleteService]: getSuggestions took 0.057 ms  -  Client - MCS_Utils:65
  16:32:00.706  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.156 ms  -  Client - MCS_Utils:65
  16:32:00.890  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XF  -  Client - MCS_Utils:65
  16:32:00.890  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:00.891  [MCS:AutocompleteService]: getSuggestions took 0.191 ms  -  Client - MCS_Utils:65
  16:32:00.891  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.302 ms  -  Client - MCS_Utils:65
  16:32:01.024  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFP  -  Client - MCS_Utils:65
  16:32:01.024  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:01.024  [MCS:AutocompleteService]: getSuggestions took 0.075 ms  -  Client - MCS_Utils:65
  16:32:01.024  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.218 ms  -  Client - MCS_Utils:65
  16:32:01.188  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPA  -  Client - MCS_Utils:65
  16:32:01.188  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:01.188  [MCS:AutocompleteService]: getSuggestions took 0.046 ms  -  Client - MCS_Utils:65
  16:32:01.188  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.148 ms  -  Client - MCS_Utils:65
  16:32:01.372  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPAN  -  Client - MCS_Utils:65
  16:32:01.372  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:01.372  [MCS:AutocompleteService]: getSuggestions took 0.044 ms  -  Client - MCS_Utils:65
  16:32:01.372  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.099 ms  -  Client - MCS_Utils:65
  16:32:01.457  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPAND  -  Client - MCS_Utils:65
  16:32:01.458  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:01.458  [MCS:AutocompleteService]: getSuggestions took 0.142 ms  -  Client - MCS_Utils:65
  16:32:01.458  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.245 ms  -  Client - MCS_Utils:65
  16:32:01.556  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPANDA  -  Client - MCS_Utils:65
  16:32:01.556  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:01.556  [MCS:AutocompleteService]: getSuggestions took 0.071 ms  -  Client - MCS_Utils:65
  16:32:01.556  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.128 ms  -  Client - MCS_Utils:65
  16:32:01.674  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPANDAF  -  Client - MCS_Utils:65
  16:32:01.674  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:01.674  [MCS:AutocompleteService]: getSuggestions took 0.211 ms  -  Client - MCS_Utils:65
  16:32:01.674  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.299 ms  -  Client - MCS_Utils:65
  16:32:01.874  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPANDAFX  -  Client - MCS_Utils:65
  16:32:01.874  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:01.874  [MCS:AutocompleteService]: getSuggestions took 0.202 ms  -  Client - MCS_Utils:65
  16:32:01.874  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.412 ms  -  Client - MCS_Utils:65
  16:32:02.024  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPANDAFX1  -  Client - MCS_Utils:65
  16:32:02.024  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:02.024  [MCS:AutocompleteService]: getSuggestions took 0.204 ms  -  Client - MCS_Utils:65
  16:32:02.025  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.348 ms  -  Client - MCS_Utils:65
  16:32:02.339  [MCS:Console]: Command sent: !mcs_commands_grantperm 1XFPANDAFX1  -  Client - MCS_Utils:65
  16:32:02.339  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  16:32:02.339  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  16:32:02.371  [MCS:MCS_Remotes]: Returned 2 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:32:02.371  [MCS:MCS_Remotes]: Command '!mcs_commands_grantperm 1XFPANDAFX1' failed for 1XFPANDAFX1: Unknown command: !mcs_commands_grantperm  -  Server - MCS_Utils:65
  16:32:02.387  [MCS:MCS_AutocompleteService]: Fetched 2 available commands  -  Client - MCS_Utils:65
  16:32:02.387  [MCS:AutocompleteService]: getAvailableCommands took 47.860 ms  -  Client - MCS_Utils:65
  16:32:02.388  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 48.033 ms  -  Client - MCS_Utils:65
  16:32:02.388  [MCS:MCS_AutocompleteService]: Returning 2 suggestions for prefix  -  Client - MCS_Utils:65
  16:32:02.388  [MCS:AutocompleteService]: getSuggestions took 48.503 ms  -  Client - MCS_Utils:65
  16:32:02.388  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 48.587 ms  -  Client - MCS_Utils:65
  16:32:02.389  [MCS:MCS_Client_Initialization]: Received server response: false  -  Client - MCS_Utils:65
  16:32:02.389  [MCS:Console]: Command failed: Unknown command: !mcs_commands_grantperm  -  Client - MCS_Utils:65
  16:32:34.291  [MCS:Console]: Console toggled via F2  -  Client - MCS_Utils:65
  16:32:34.993  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  16:32:34.994  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  16:32:35.020  [MCS:MCS_Remotes]: Returned 2 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:32:35.053  [MCS:MCS_AutocompleteService]: Fetched 2 available commands  -  Client - MCS_Utils:65
  16:32:35.053  [MCS:AutocompleteService]: getAvailableCommands took 59.444 ms  -  Client - MCS_Utils:65
  16:32:35.053  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 59.529 ms  -  Client - MCS_Utils:65
  16:32:35.054  [MCS:MCS_AutocompleteService]: Returning 2 suggestions for prefix  -  Client - MCS_Utils:65
  16:32:35.054  [MCS:AutocompleteService]: getSuggestions took 60.019 ms  -  Client - MCS_Utils:65
  16:32:35.054  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 60.085 ms  -  Client - MCS_Utils:65
  16:32:36.356  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm  -  Client - MCS_Utils:65
  16:32:36.356  [MCS:MCS_AutocompleteService]: Returning 1 cached suggestions  -  Client - MCS_Utils:65
  16:32:36.356  [MCS:AutocompleteService]: getSuggestions took 0.164 ms  -  Client - MCS_Utils:65
  16:32:36.356  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.251 ms  -  Client - MCS_Utils:65
  16:32:37.106  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm   -  Client - MCS_Utils:65
  16:32:37.106  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:37.106  [MCS:AutocompleteService]: getSuggestions took 0.073 ms  -  Client - MCS_Utils:65
  16:32:37.106  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.154 ms  -  Client - MCS_Utils:65
  16:32:37.492  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1  -  Client - MCS_Utils:65
  16:32:37.492  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:37.492  [MCS:AutocompleteService]: getSuggestions took 0.150 ms  -  Client - MCS_Utils:65
  16:32:37.492  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.265 ms  -  Client - MCS_Utils:65
  16:32:38.039  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1X  -  Client - MCS_Utils:65
  16:32:38.039  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:38.039  [MCS:AutocompleteService]: getSuggestions took 0.133 ms  -  Client - MCS_Utils:65
  16:32:38.039  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.163 ms  -  Client - MCS_Utils:65
  16:32:38.272  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XF  -  Client - MCS_Utils:65
  16:32:38.272  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:38.272  [MCS:AutocompleteService]: getSuggestions took 0.106 ms  -  Client - MCS_Utils:65
  16:32:38.272  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.187 ms  -  Client - MCS_Utils:65
  16:32:38.557  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFP  -  Client - MCS_Utils:65
  16:32:38.557  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:38.557  [MCS:AutocompleteService]: getSuggestions took 0.097 ms  -  Client - MCS_Utils:65
  16:32:38.557  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.178 ms  -  Client - MCS_Utils:65
  16:32:38.738  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPD  -  Client - MCS_Utils:65
  16:32:38.739  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:38.739  [MCS:AutocompleteService]: getSuggestions took 0.095 ms  -  Client - MCS_Utils:65
  16:32:38.739  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.174 ms  -  Client - MCS_Utils:65
  16:32:38.888  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPDA  -  Client - MCS_Utils:65
  16:32:38.889  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:38.889  [MCS:AutocompleteService]: getSuggestions took 0.126 ms  -  Client - MCS_Utils:65
  16:32:38.889  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.226 ms  -  Client - MCS_Utils:65
  16:32:39.138  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPD  -  Client - MCS_Utils:65
  16:32:39.138  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:39.138  [MCS:AutocompleteService]: getSuggestions took 0.079 ms  -  Client - MCS_Utils:65
  16:32:39.139  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.203 ms  -  Client - MCS_Utils:65
  16:32:39.289  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFP  -  Client - MCS_Utils:65
  16:32:39.289  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:39.290  [MCS:AutocompleteService]: getSuggestions took 0.221 ms  -  Client - MCS_Utils:65
  16:32:39.290  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.346 ms  -  Client - MCS_Utils:65
  16:32:39.389  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPA  -  Client - MCS_Utils:65
  16:32:39.390  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:39.390  [MCS:AutocompleteService]: getSuggestions took 0.192 ms  -  Client - MCS_Utils:65
  16:32:39.390  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.262 ms  -  Client - MCS_Utils:65
  16:32:39.606  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPAN  -  Client - MCS_Utils:65
  16:32:39.606  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:39.607  [MCS:AutocompleteService]: getSuggestions took 0.192 ms  -  Client - MCS_Utils:65
  16:32:39.607  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.420 ms  -  Client - MCS_Utils:65
  16:32:40.822  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPAND  -  Client - MCS_Utils:65
  16:32:40.822  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:40.822  [MCS:AutocompleteService]: getSuggestions took 0.202 ms  -  Client - MCS_Utils:65
  16:32:40.822  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.276 ms  -  Client - MCS_Utils:65
  16:32:40.923  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPANDA  -  Client - MCS_Utils:65
  16:32:40.923  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:40.923  [MCS:AutocompleteService]: getSuggestions took 0.174 ms  -  Client - MCS_Utils:65
  16:32:40.923  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.313 ms  -  Client - MCS_Utils:65
  16:32:41.105  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPANDAF  -  Client - MCS_Utils:65
  16:32:41.105  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:41.105  [MCS:AutocompleteService]: getSuggestions took 0.129 ms  -  Client - MCS_Utils:65
  16:32:41.105  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.170 ms  -  Client - MCS_Utils:65
  16:32:41.340  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPANDAFX  -  Client - MCS_Utils:65
  16:32:41.340  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:41.340  [MCS:AutocompleteService]: getSuggestions took 0.177 ms  -  Client - MCS_Utils:65
  16:32:41.340  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.337 ms  -  Client - MCS_Utils:65
  16:32:41.523  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPANDAFX1  -  Client - MCS_Utils:65
  16:32:41.523  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:41.523  [MCS:AutocompleteService]: getSuggestions took 0.149 ms  -  Client - MCS_Utils:65
  16:32:41.523  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.275 ms  -  Client - MCS_Utils:65
  16:32:54.956  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPANDAFX1   -  Client - MCS_Utils:65
  16:32:54.956  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:54.957  [MCS:AutocompleteService]: getSuggestions took 0.113 ms  -  Client - MCS_Utils:65
  16:32:54.957  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.245 ms  -  Client - MCS_Utils:65
  16:32:55.272  [MCS:MCS_AutocompleteService]: Getting suggestions for: !mcs_commands_grantperm 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:32:55.272  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:32:55.272  [MCS:AutocompleteService]: getSuggestions took 0.130 ms  -  Client - MCS_Utils:65
  16:32:55.272  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.250 ms  -  Client - MCS_Utils:65
  16:32:55.637  [MCS:Console]: Command sent: !mcs_commands_grantperm 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:32:55.637  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  16:32:55.637  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  16:32:55.670  [MCS:MCS_Remotes]: Returned 2 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:32:55.671  [MCS:MCS_Remotes]: Command '!mcs_commands_grantperm 1XFPANDAFX1 3' failed for 1XFPANDAFX1: Unknown command: !mcs_commands_grantperm  -  Server - MCS_Utils:65
  16:32:55.703  [MCS:MCS_AutocompleteService]: Fetched 2 available commands  -  Client - MCS_Utils:65
  16:32:55.703  [MCS:AutocompleteService]: getAvailableCommands took 65.847 ms  -  Client - MCS_Utils:65
  16:32:55.703  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 65.911 ms  -  Client - MCS_Utils:65
  16:32:55.703  [MCS:MCS_AutocompleteService]: Returning 2 suggestions for prefix  -  Client - MCS_Utils:65
  16:32:55.703  [MCS:AutocompleteService]: getSuggestions took 65.994 ms  -  Client - MCS_Utils:65
  16:32:55.703  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 66.085 ms  -  Client - MCS_Utils:65
  16:32:55.704  [MCS:MCS_Client_Initialization]: Received server response: false  -  Client - MCS_Utils:65
  16:32:55.704  [MCS:Console]: Command failed: Unknown command: !mcs_commands_grantperm  -  Client - MCS_Utils:65
  16:33:12.755  [MCS:Console]: Console toggled via F2  -  Client - MCS_Utils:65
  16:33:13.188  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  16:33:13.188  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  16:33:13.219  [MCS:MCS_Remotes]: Returned 2 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:33:13.236  [MCS:MCS_AutocompleteService]: Fetched 2 available commands  -  Client - MCS_Utils:65
  16:33:13.236  [MCS:AutocompleteService]: getAvailableCommands took 47.582 ms  -  Client - MCS_Utils:65
  16:33:13.236  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 47.703 ms  -  Client - MCS_Utils:65
  16:33:13.236  [MCS:MCS_AutocompleteService]: Returning 2 suggestions for prefix  -  Client - MCS_Utils:65
  16:33:13.236  [MCS:AutocompleteService]: getSuggestions took 47.924 ms  -  Client - MCS_Utils:65
  16:33:13.236  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 47.980 ms  -  Client - MCS_Utils:65
  16:33:14.756  [MCS:MCS_AutocompleteService]: Getting suggestions for: !g  -  Client - MCS_Utils:65
  16:33:14.786  [MCS:MCS_Remotes]: Returned 1 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:33:14.819  [MCS:MCS_AutocompleteService]: Fetched 1 suggestions from server  -  Client - MCS_Utils:65
  16:33:14.820  [MCS:AutocompleteService]: getSuggestions took 63.502 ms  -  Client - MCS_Utils:65
  16:33:14.820  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 63.660 ms  -  Client - MCS_Utils:65
  16:33:14.956  [MCS:MCS_AutocompleteService]: Getting suggestions for: !gr  -  Client - MCS_Utils:65
  16:33:14.956  [MCS:MCS_AutocompleteService]: Returning 1 cached suggestions  -  Client - MCS_Utils:65
  16:33:14.956  [MCS:AutocompleteService]: getSuggestions took 0.056 ms  -  Client - MCS_Utils:65
  16:33:14.957  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.158 ms  -  Client - MCS_Utils:65
  16:33:15.054  [MCS:MCS_AutocompleteService]: Getting suggestions for: !gra  -  Client - MCS_Utils:65
  16:33:15.055  [MCS:MCS_AutocompleteService]: Returning 1 cached suggestions  -  Client - MCS_Utils:65
  16:33:15.055  [MCS:AutocompleteService]: getSuggestions took 0.072 ms  -  Client - MCS_Utils:65
  16:33:15.055  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.119 ms  -  Client - MCS_Utils:65
  16:33:15.171  [MCS:MCS_AutocompleteService]: Getting suggestions for: !gran  -  Client - MCS_Utils:65
  16:33:15.172  [MCS:MCS_AutocompleteService]: Returning 1 cached suggestions  -  Client - MCS_Utils:65
  16:33:15.172  [MCS:AutocompleteService]: getSuggestions took 0.101 ms  -  Client - MCS_Utils:65
  16:33:15.172  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.250 ms  -  Client - MCS_Utils:65
  16:33:15.306  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grant  -  Client - MCS_Utils:65
  16:33:15.306  [MCS:MCS_AutocompleteService]: Returning 1 cached suggestions  -  Client - MCS_Utils:65
  16:33:15.306  [MCS:AutocompleteService]: getSuggestions took 0.122 ms  -  Client - MCS_Utils:65
  16:33:15.306  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.225 ms  -  Client - MCS_Utils:65
  16:33:16.139  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantp  -  Client - MCS_Utils:65
  16:33:16.139  [MCS:MCS_AutocompleteService]: Returning 1 cached suggestions  -  Client - MCS_Utils:65
  16:33:16.139  [MCS:AutocompleteService]: getSuggestions took 0.101 ms  -  Client - MCS_Utils:65
  16:33:16.139  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.170 ms  -  Client - MCS_Utils:65
  16:33:16.256  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantpe  -  Client - MCS_Utils:65
  16:33:16.256  [MCS:MCS_AutocompleteService]: Returning 1 cached suggestions  -  Client - MCS_Utils:65
  16:33:16.256  [MCS:AutocompleteService]: getSuggestions took 0.209 ms  -  Client - MCS_Utils:65
  16:33:16.256  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.350 ms  -  Client - MCS_Utils:65
  16:33:16.304  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantper  -  Client - MCS_Utils:65
  16:33:16.305  [MCS:MCS_AutocompleteService]: Returning 1 cached suggestions  -  Client - MCS_Utils:65
  16:33:16.305  [MCS:AutocompleteService]: getSuggestions took 0.113 ms  -  Client - MCS_Utils:65
  16:33:16.305  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.233 ms  -  Client - MCS_Utils:65
  16:33:16.405  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm  -  Client - MCS_Utils:65
  16:33:16.405  [MCS:MCS_AutocompleteService]: Returning 1 cached suggestions  -  Client - MCS_Utils:65
  16:33:16.405  [MCS:AutocompleteService]: getSuggestions took 0.039 ms  -  Client - MCS_Utils:65
  16:33:16.405  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.065 ms  -  Client - MCS_Utils:65
  16:33:16.672  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm   -  Client - MCS_Utils:65
  16:33:16.673  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:16.673  [MCS:AutocompleteService]: getSuggestions took 0.179 ms  -  Client - MCS_Utils:65
  16:33:16.673  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.309 ms  -  Client - MCS_Utils:65
  16:33:17.422  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm !mcs_commands_grantperm 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:17.422  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:17.422  [MCS:AutocompleteService]: getSuggestions took 0.055 ms  -  Client - MCS_Utils:65
  16:33:17.422  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.195 ms  -  Client - MCS_Utils:65
  16:33:20.221  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm mcs_commands_grantperm 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:20.221  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:20.221  [MCS:AutocompleteService]: getSuggestions took 0.071 ms  -  Client - MCS_Utils:65
  16:33:20.221  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.104 ms  -  Client - MCS_Utils:65
  16:33:21.871  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:21.872  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:21.872  [MCS:AutocompleteService]: getSuggestions took 0.200 ms  -  Client - MCS_Utils:65
  16:33:21.872  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.283 ms  -  Client - MCS_Utils:65
  16:33:23.188  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantper 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:23.188  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:23.188  [MCS:AutocompleteService]: getSuggestions took 0.161 ms  -  Client - MCS_Utils:65
  16:33:23.188  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.253 ms  -  Client - MCS_Utils:65
  16:33:23.821  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantper, 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:23.821  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:23.821  [MCS:AutocompleteService]: getSuggestions took 0.043 ms  -  Client - MCS_Utils:65
  16:33:23.822  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.144 ms  -  Client - MCS_Utils:65
  16:33:23.839  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantper,m 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:23.839  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:23.839  [MCS:AutocompleteService]: getSuggestions took 0.052 ms  -  Client - MCS_Utils:65
  16:33:23.839  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.086 ms  -  Client - MCS_Utils:65
  16:33:24.238  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantper, 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:24.238  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:24.238  [MCS:AutocompleteService]: getSuggestions took 0.140 ms  -  Client - MCS_Utils:65
  16:33:24.238  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.241 ms  -  Client - MCS_Utils:65
  16:33:24.405  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantper 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:24.405  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:24.405  [MCS:AutocompleteService]: getSuggestions took 0.224 ms  -  Client - MCS_Utils:65
  16:33:24.405  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.493 ms  -  Client - MCS_Utils:65
  16:33:24.621  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantper, 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:24.621  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:24.621  [MCS:AutocompleteService]: getSuggestions took 0.048 ms  -  Client - MCS_Utils:65
  16:33:24.621  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.085 ms  -  Client - MCS_Utils:65
  16:33:24.638  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantper,m 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:24.638  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:24.638  [MCS:AutocompleteService]: getSuggestions took 0.077 ms  -  Client - MCS_Utils:65
  16:33:24.638  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.192 ms  -  Client - MCS_Utils:65
  16:33:25.005  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantper, 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:25.005  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:25.005  [MCS:AutocompleteService]: getSuggestions took 0.163 ms  -  Client - MCS_Utils:65
  16:33:25.005  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.250 ms  -  Client - MCS_Utils:65
  16:33:25.153  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantper 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:25.153  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:25.153  [MCS:AutocompleteService]: getSuggestions took 0.062 ms  -  Client - MCS_Utils:65
  16:33:25.153  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.126 ms  -  Client - MCS_Utils:65
  16:33:25.357  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:25.357  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:25.357  [MCS:AutocompleteService]: getSuggestions took 0.060 ms  -  Client - MCS_Utils:65
  16:33:25.357  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.132 ms  -  Client - MCS_Utils:65
  16:33:26.421  [MCS:Console]: Command sent: !grantperm 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:26.422  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  16:33:26.422  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  16:33:26.452  [MCS:MCS_Remotes]: Returned 2 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:33:26.453  [MCS:MCS_Remotes]: Command '!grantperm 1XFPANDAFX1 3' failed for 1XFPANDAFX1: Unknown command: !grantperm  -  Server - MCS_Utils:65
  16:33:26.486  [MCS:MCS_AutocompleteService]: Fetched 2 available commands  -  Client - MCS_Utils:65
  16:33:26.486  [MCS:AutocompleteService]: getAvailableCommands took 64.112 ms  -  Client - MCS_Utils:65
  16:33:26.486  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 64.237 ms  -  Client - MCS_Utils:65
  16:33:26.486  [MCS:MCS_AutocompleteService]: Returning 2 suggestions for prefix  -  Client - MCS_Utils:65
  16:33:26.486  [MCS:AutocompleteService]: getSuggestions took 64.435 ms  -  Client - MCS_Utils:65
  16:33:26.486  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 64.518 ms  -  Client - MCS_Utils:65
  16:33:26.487  [MCS:MCS_Client_Initialization]: Received server response: false  -  Client - MCS_Utils:65
  16:33:26.488  [MCS:Console]: Command failed: Unknown command: !grantperm  -  Client - MCS_Utils:65
  16:33:32.154  [MCS:Console]: Console toggled via F2  -  Client - MCS_Utils:65
  16:33:33.725  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 3  -  Client - MCS_Utils:65
  16:33:33.725  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:33.726  [MCS:AutocompleteService]: getSuggestions took 0.069 ms  -  Client - MCS_Utils:65
  16:33:33.726  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.145 ms  -  Client - MCS_Utils:65
  16:33:34.887  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1   -  Client - MCS_Utils:65
  16:33:34.887  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:34.887  [MCS:AutocompleteService]: getSuggestions took 0.050 ms  -  Client - MCS_Utils:65
  16:33:34.887  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.112 ms  -  Client - MCS_Utils:65
  16:33:35.788  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 S  -  Client - MCS_Utils:65
  16:33:35.789  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:35.789  [MCS:AutocompleteService]: getSuggestions took 0.243 ms  -  Client - MCS_Utils:65
  16:33:35.789  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.360 ms  -  Client - MCS_Utils:65
  16:33:36.011  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 Se  -  Client - MCS_Utils:65
  16:33:36.011  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:36.011  [MCS:AutocompleteService]: getSuggestions took 0.185 ms  -  Client - MCS_Utils:65
  16:33:36.011  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.365 ms  -  Client - MCS_Utils:65
  16:33:36.169  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 Sen  -  Client - MCS_Utils:65
  16:33:36.170  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:36.170  [MCS:AutocompleteService]: getSuggestions took 0.109 ms  -  Client - MCS_Utils:65
  16:33:36.170  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.197 ms  -  Client - MCS_Utils:65
  16:33:36.354  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 Seni  -  Client - MCS_Utils:65
  16:33:36.354  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:36.354  [MCS:AutocompleteService]: getSuggestions took 0.054 ms  -  Client - MCS_Utils:65
  16:33:36.354  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.125 ms  -  Client - MCS_Utils:65
  16:33:36.537  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 Senio  -  Client - MCS_Utils:65
  16:33:36.537  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:36.537  [MCS:AutocompleteService]: getSuggestions took 0.147 ms  -  Client - MCS_Utils:65
  16:33:36.538  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.249 ms  -  Client - MCS_Utils:65
  16:33:36.605  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 Senior  -  Client - MCS_Utils:65
  16:33:36.605  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:36.605  [MCS:AutocompleteService]: getSuggestions took 0.166 ms  -  Client - MCS_Utils:65
  16:33:36.606  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.391 ms  -  Client - MCS_Utils:65
  16:33:42.271  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 SeniorM  -  Client - MCS_Utils:65
  16:33:42.271  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:42.271  [MCS:AutocompleteService]: getSuggestions took 0.056 ms  -  Client - MCS_Utils:65
  16:33:42.271  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.205 ms  -  Client - MCS_Utils:65
  16:33:42.456  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 SeniorMo  -  Client - MCS_Utils:65
  16:33:42.456  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:42.456  [MCS:AutocompleteService]: getSuggestions took 0.146 ms  -  Client - MCS_Utils:65
  16:33:42.456  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.309 ms  -  Client - MCS_Utils:65
  16:33:42.554  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 SeniorMod  -  Client - MCS_Utils:65
  16:33:42.554  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:42.555  [MCS:AutocompleteService]: getSuggestions took 0.082 ms  -  Client - MCS_Utils:65
  16:33:42.555  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.193 ms  -  Client - MCS_Utils:65
  16:33:42.621  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 SeniorMode  -  Client - MCS_Utils:65
  16:33:42.621  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:42.621  [MCS:AutocompleteService]: getSuggestions took 0.159 ms  -  Client - MCS_Utils:65
  16:33:42.621  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.246 ms  -  Client - MCS_Utils:65
  16:33:42.771  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 SeniorModer  -  Client - MCS_Utils:65
  16:33:42.771  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:42.771  [MCS:AutocompleteService]: getSuggestions took 0.185 ms  -  Client - MCS_Utils:65
  16:33:42.772  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.437 ms  -  Client - MCS_Utils:65
  16:33:42.888  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 SeniorModera  -  Client - MCS_Utils:65
  16:33:42.888  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:42.888  [MCS:AutocompleteService]: getSuggestions took 0.076 ms  -  Client - MCS_Utils:65
  16:33:42.888  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.122 ms  -  Client - MCS_Utils:65
  16:33:42.971  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 SeniorModerat  -  Client - MCS_Utils:65
  16:33:42.971  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:42.971  [MCS:AutocompleteService]: getSuggestions took 0.102 ms  -  Client - MCS_Utils:65
  16:33:42.972  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.237 ms  -  Client - MCS_Utils:65
  16:33:43.104  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 SeniorModerato  -  Client - MCS_Utils:65
  16:33:43.104  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:43.104  [MCS:AutocompleteService]: getSuggestions took 0.039 ms  -  Client - MCS_Utils:65
  16:33:43.104  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.065 ms  -  Client - MCS_Utils:65
  16:33:43.188  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 SeniorModerator  -  Client - MCS_Utils:65
  16:33:43.188  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:43.188  [MCS:AutocompleteService]: getSuggestions took 0.076 ms  -  Client - MCS_Utils:65
  16:33:43.188  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.205 ms  -  Client - MCS_Utils:65
  16:33:43.271  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1 SeniorModerators  -  Client - MCS_Utils:65
  16:33:43.271  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  16:33:43.271  [MCS:AutocompleteService]: getSuggestions took 0.120 ms  -  Client - MCS_Utils:65
  16:33:43.271  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.211 ms  -  Client - MCS_Utils:65
  16:33:43.555  [MCS:Console]: Command sent: !grantperm 1XFPANDAFX1 SeniorModerators  -  Client - MCS_Utils:65
  16:33:43.555  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  16:33:43.555  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  16:33:43.587  [MCS:MCS_Remotes]: Returned 2 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  16:33:43.588  [MCS:MCS_Remotes]: Command '!grantperm 1XFPANDAFX1 SeniorModerators' failed for 1XFPANDAFX1: Unknown command: !grantperm  -  Server - MCS_Utils:65
  16:33:43.619  [MCS:MCS_AutocompleteService]: Fetched 2 available commands  -  Client - MCS_Utils:65
  16:33:43.619  [MCS:AutocompleteService]: getAvailableCommands took 63.263 ms  -  Client - MCS_Utils:65
  16:33:43.619  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 63.397 ms  -  Client - MCS_Utils:65
  16:33:43.619  [MCS:MCS_AutocompleteService]: Returning 2 suggestions for prefix  -  Client - MCS_Utils:65
  16:33:43.619  [MCS:AutocompleteService]: getSuggestions took 63.720 ms  -  Client - MCS_Utils:65
  16:33:43.619  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 63.825 ms  -  Client - MCS_Utils:65
  16:33:43.620  [MCS:MCS_Client_Initialization]: Received server response: false  -  Client - MCS_Utils:65
  16:33:43.620  [MCS:Console]: Command failed: Unknown command: !grantperm  -  Client - MCS_Utils:65
