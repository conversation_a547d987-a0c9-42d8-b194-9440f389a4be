  17:09:48.698  [MCS:MCS_Server_Initialization]: Starting initialization...  -  Server - MCS_Utils:65
  17:09:48.698  [MCS:PermissionService]: Initialized  -  Server - MCS_Utils:65
  17:09:48.698  [MCS:MiddlewareSystem]: Initializing middleware system...  -  Server - MCS_Utils:65
  17:09:48.698  [MCS:MiddlewareSystem]: Creating fallback middleware implementations...  -  Server - MCS_Utils:65
  17:09:48.698  [MCS:MiddlewareSystem]: Fallback middleware created successfully  -  Server - MCS_Utils:65
  17:09:48.699  [MCS:MiddlewareSystem]: Loading middleware chain...  -  Server - MCS_Utils:65
  17:09:48.699  [MCS:Authentication]: Authentication middleware initialized successfully  -  Server - MCS_Utils:65
  17:09:48.699  [MCS:MiddlewareSystem]: Authentication middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  17:09:48.699  [MCS:MiddlewareSystem]: LoadMiddleware: Authentication took 0.470 ms  -  Server - MCS_Utils:65
  17:09:48.699  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Authentication took 0.561 ms  -  Server - MCS_Utils:65
  17:09:48.700  [MCS:Permission]: Permission middleware initialized successfully  -  Server - MCS_Utils:65
  17:09:48.700  [MCS:MiddlewareSystem]: Permission middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  17:09:48.700  [MCS:MiddlewareSystem]: LoadMiddleware: Permission took 0.310 ms  -  Server - MCS_Utils:65
  17:09:48.700  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Permission took 0.394 ms  -  Server - MCS_Utils:65
  17:09:48.700  [MCS:RateLimiter]: RateLimiterInit took 0.008 ms  -  Server - MCS_Utils:65
  17:09:48.700  [MCS:RateLimiter]: PerformanceMonitor: RateLimiterInit took 0.042 ms  -  Server - MCS_Utils:65
  17:09:48.700  [MCS:RateLimiter]: Rate limiter middleware initialized successfully  -  Server - MCS_Utils:65
  17:09:48.700  [MCS:MiddlewareSystem]: RateLimiter middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  17:09:48.700  [MCS:MiddlewareSystem]: LoadMiddleware: RateLimiter took 0.635 ms  -  Server - MCS_Utils:65
  17:09:48.701  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: RateLimiter took 0.670 ms  -  Server - MCS_Utils:65
  17:09:48.701  [MCS:Logger]: Logger initialized successfully  -  Server - MCS_Utils:65
  17:09:48.701  [MCS:MiddlewareSystem]: Logger middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  17:09:48.701  [MCS:MiddlewareSystem]: LoadMiddleware: Logger took 0.488 ms  -  Server - MCS_Utils:65
  17:09:48.701  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Logger took 0.608 ms  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:Analytics]: Analytics initialized successfully  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: Analytics middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: LoadMiddleware: Analytics took 0.375 ms  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: Analytics took 0.426 ms  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:InputValidation]: InputValidation middleware initialized successfully  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: InputValidation middleware loaded successfully (v2.0.0)  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: LoadMiddleware: InputValidation took 0.191 ms  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddleware: InputValidation took 0.231 ms  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: Middleware chain loaded with 6 modules  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: LoadMiddlewareChain took 3.664 ms  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: PerformanceMonitor: LoadMiddlewareChain took 3.724 ms  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: Middleware system initialized successfully with 6 modules  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: MiddlewareInit took 4.211 ms  -  Server - MCS_Utils:65
  17:09:48.702  [MCS:MiddlewareSystem]: PerformanceMonitor: MiddlewareInit took 4.269 ms  -  Server - MCS_Utils:65
  17:09:48.703  [MCS:MCS_Remotes]: Remotes system initialized successfully  -  Server - MCS_Utils:65
  17:09:48.703  [MCS:MCS_Server_Initialization]: Initialization completed.  -  Server - MCS_Utils:65
  17:09:48.843  [MCS:MCS_CommandParser]: Module initialized  -  Client - MCS_Utils:65
  17:09:48.843  [MCS:MCS_AutocompleteService]: Module initialized  -  Client - MCS_Utils:65
  17:09:53.472  [MCS:MiddlewareSystem]: Middleware system already initialized  -  Server - MCS_Utils:65
  17:09:53.602  [MCS:Console]: Initializing...  -  Client - MCS_Utils:65
  17:09:53.690  [MCS:FeedbackDisplay]: Initializing...  -  Client - MCS_Utils:65
  17:09:53.690  [MCS:MCS_AutocompleteService]: Initializing service  -  Client - MCS_Utils:65
  17:09:53.690  [MCS:MCS_AutocompleteService]: Service initialized successfully  -  Client - MCS_Utils:65
  17:09:53.690  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  17:09:53.690  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  17:09:53.690  [MCS:MCS_Client_Initialization]: MCS Client initialized  -  Client - MCS_Utils:65
  17:09:53.719  [MCS:MCS_Remotes]: Returned 8 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  17:09:53.753  [MCS:MCS_AutocompleteService]: Fetched 8 available commands  -  Client - MCS_Utils:65
  17:09:53.754  [MCS:AutocompleteService]: getAvailableCommands took 63.259 ms  -  Client - MCS_Utils:65
  17:09:53.754  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 63.406 ms  -  Client - MCS_Utils:65
  17:09:53.754  [MCS:MCS_AutocompleteService]: Returning 8 suggestions for prefix  -  Client - MCS_Utils:65
  17:09:53.754  [MCS:AutocompleteService]: getSuggestions took 63.623 ms  -  Client - MCS_Utils:65
  17:09:53.754  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 63.726 ms  -  Client - MCS_Utils:65
  17:10:00.571  [MCS:Console]: Console toggled via F2  -  Client - MCS_Utils:65
  17:10:01.221  [MCS:MCS_AutocompleteService]: Getting suggestions for: !  -  Client - MCS_Utils:65
  17:10:01.221  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  17:10:01.252  [MCS:MCS_Remotes]: Returned 8 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  17:10:01.286  [MCS:MCS_AutocompleteService]: Fetched 8 available commands  -  Client - MCS_Utils:65
  17:10:01.286  [MCS:AutocompleteService]: getAvailableCommands took 64.390 ms  -  Client - MCS_Utils:65
  17:10:01.286  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 64.451 ms  -  Client - MCS_Utils:65
  17:10:01.286  [MCS:MCS_AutocompleteService]: Returning 8 suggestions for prefix  -  Client - MCS_Utils:65
  17:10:01.286  [MCS:AutocompleteService]: getSuggestions took 64.664 ms  -  Client - MCS_Utils:65
  17:10:01.286  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 64.750 ms  -  Client - MCS_Utils:65
  17:10:12.171  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm  -  Client - MCS_Utils:65
  17:10:12.202  [MCS:MCS_Remotes]: Returned 1 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  17:10:12.235  [MCS:MCS_AutocompleteService]: Fetched 1 suggestions from server  -  Client - MCS_Utils:65
  17:10:12.235  [MCS:AutocompleteService]: getSuggestions took 64.576 ms  -  Client - MCS_Utils:65
  17:10:12.236  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 64.721 ms  -  Client - MCS_Utils:65
  17:10:12.904  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm   -  Client - MCS_Utils:65
  17:10:12.904  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:12.905  [MCS:AutocompleteService]: getSuggestions took 0.073 ms  -  Client - MCS_Utils:65
  17:10:12.905  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.147 ms  -  Client - MCS_Utils:65
  17:10:13.538  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1  -  Client - MCS_Utils:65
  17:10:13.539  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:13.539  [MCS:AutocompleteService]: getSuggestions took 0.173 ms  -  Client - MCS_Utils:65
  17:10:13.539  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.352 ms  -  Client - MCS_Utils:65
  17:10:14.538  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1X  -  Client - MCS_Utils:65
  17:10:14.539  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:14.539  [MCS:AutocompleteService]: getSuggestions took 0.166 ms  -  Client - MCS_Utils:65
  17:10:14.539  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.345 ms  -  Client - MCS_Utils:65
  17:10:14.838  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XF  -  Client - MCS_Utils:65
  17:10:14.838  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:14.838  [MCS:AutocompleteService]: getSuggestions took 0.108 ms  -  Client - MCS_Utils:65
  17:10:14.838  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.161 ms  -  Client - MCS_Utils:65
  17:10:15.554  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFP  -  Client - MCS_Utils:65
  17:10:15.554  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:15.554  [MCS:AutocompleteService]: getSuggestions took 0.039 ms  -  Client - MCS_Utils:65
  17:10:15.554  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.062 ms  -  Client - MCS_Utils:65
  17:10:15.704  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPA  -  Client - MCS_Utils:65
  17:10:15.704  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:15.704  [MCS:AutocompleteService]: getSuggestions took 0.116 ms  -  Client - MCS_Utils:65
  17:10:15.704  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.178 ms  -  Client - MCS_Utils:65
  17:10:16.189  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPAN  -  Client - MCS_Utils:65
  17:10:16.189  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:16.189  [MCS:AutocompleteService]: getSuggestions took 0.193 ms  -  Client - MCS_Utils:65
  17:10:16.189  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.263 ms  -  Client - MCS_Utils:65
  17:10:16.389  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPAND  -  Client - MCS_Utils:65
  17:10:16.390  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:16.390  [MCS:AutocompleteService]: getSuggestions took 0.066 ms  -  Client - MCS_Utils:65
  17:10:16.390  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.132 ms  -  Client - MCS_Utils:65
  17:10:16.507  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDA  -  Client - MCS_Utils:65
  17:10:16.507  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:16.507  [MCS:AutocompleteService]: getSuggestions took 0.082 ms  -  Client - MCS_Utils:65
  17:10:16.507  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.122 ms  -  Client - MCS_Utils:65
  17:10:16.704  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAF  -  Client - MCS_Utils:65
  17:10:16.704  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:16.704  [MCS:AutocompleteService]: getSuggestions took 0.038 ms  -  Client - MCS_Utils:65
  17:10:16.704  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.134 ms  -  Client - MCS_Utils:65
  17:10:16.971  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX  -  Client - MCS_Utils:65
  17:10:16.971  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:16.971  [MCS:AutocompleteService]: getSuggestions took 0.037 ms  -  Client - MCS_Utils:65
  17:10:16.971  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.062 ms  -  Client - MCS_Utils:65
  17:10:17.204  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1  -  Client - MCS_Utils:65
  17:10:17.204  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:17.204  [MCS:AutocompleteService]: getSuggestions took 0.043 ms  -  Client - MCS_Utils:65
  17:10:17.204  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.070 ms  -  Client - MCS_Utils:65
  17:10:18.187  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1   -  Client - MCS_Utils:65
  17:10:18.187  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:18.187  [MCS:AutocompleteService]: getSuggestions took 0.052 ms  -  Client - MCS_Utils:65
  17:10:18.187  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.150 ms  -  Client - MCS_Utils:65
  17:10:19.254  [MCS:MCS_AutocompleteService]: Getting suggestions for: !grantperm 1XFPANDAFX1  -  Client - MCS_Utils:65
  17:10:19.254  [MCS:MCS_AutocompleteService]: Returning 0 cached suggestions  -  Client - MCS_Utils:65
  17:10:19.254  [MCS:AutocompleteService]: getSuggestions took 0.057 ms  -  Client - MCS_Utils:65
  17:10:19.254  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 0.137 ms  -  Client - MCS_Utils:65
  17:10:19.520  [MCS:Console]: Command sent: !grantperm 1XFPANDAFX1  -  Client - MCS_Utils:65
  17:10:19.521  [MCS:MCS_AutocompleteService]: Getting suggestions for:   -  Client - MCS_Utils:65
  17:10:19.521  [MCS:MCS_AutocompleteService]: Getting available commands  -  Client - MCS_Utils:65
  17:10:19.552  [MCS:MCS_Remotes]: Returned 8 autocomplete suggestions for 1XFPANDAFX1  -  Server - MCS_Utils:65
  17:10:19.552  [MCS:MCS_Remotes]: Command '!grantperm 1XFPANDAFX1' failed for 1XFPANDAFX1: Unknown command: !grantperm  -  Server - MCS_Utils:65
  17:10:19.585  [MCS:MCS_AutocompleteService]: Fetched 8 available commands  -  Client - MCS_Utils:65
  17:10:19.586  [MCS:AutocompleteService]: getAvailableCommands took 64.983 ms  -  Client - MCS_Utils:65
  17:10:19.586  [MCS:AutocompleteService]: PerformanceMonitor: getAvailableCommands took 65.060 ms  -  Client - MCS_Utils:65
  17:10:19.586  [MCS:MCS_AutocompleteService]: Returning 8 suggestions for prefix  -  Client - MCS_Utils:65
  17:10:19.586  [MCS:AutocompleteService]: getSuggestions took 65.334 ms  -  Client - MCS_Utils:65
  17:10:19.586  [MCS:AutocompleteService]: PerformanceMonitor: getSuggestions took 65.435 ms  -  Client - MCS_Utils:65
  17:10:19.587  [MCS:MCS_Client_Initialization]: Received server response: false  -  Client - MCS_Utils:65
  17:10:19.588  [MCS:Console]: Command failed: Unknown command: !grantperm  -  Client - MCS_Utils:65
